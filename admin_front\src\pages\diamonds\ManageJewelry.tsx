import React from 'react';
import Input from '../../components/ui/Input';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import { useManagementState } from '../../hooks/useManagementState';
import toast from 'react-hot-toast';

const ManageJewelry: React.FC = () => {
  const {
    items: jewelry,
    isLoading,
    error,
    newItem: newJewelry,
    setNewItem: setNewJewelry,
    addItemMutation: addJewelryMutation,
    deleteItemMutation: deleteJewelryMutation,
  } = useManagementState({
    queryKey: 'jewelry',
    fetchUrl: '/diamonds/jewelry',
    createUrl: '/diamonds/jewelry',
    deleteUrl: '/diamonds/jewelry',
  });

  const handleAddJewelry = () => {
    if (!newJewelry.trim()) {
      toast.error('Jewelry name cannot be empty');
      return;
    }
    if (newJewelry.length > 100) {
      toast.error('Jewelry name must be less than 100 characters');
      return;
    }
    addJewelryMutation.mutate(newJewelry);
  };

  const handleDeleteJewelry = (jewelryId: number) => {
    if (window.confirm('Are you sure you want to delete this jewelry item?')) {
      deleteJewelryMutation.mutate(jewelryId);
    }
  };

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Manage Jewelry Items</h1>
      <Card>
        <div className="space-y-4">
          <Input
            label="New Jewelry Item"
            value={newJewelry}
            onChange={(e) => setNewJewelry(e.target.value)}
            placeholder="Enter jewelry name"
          />
          <Button onClick={handleAddJewelry} isLoading={addJewelryMutation.isPending}>
            Add Jewelry
          </Button>
        </div>
      </Card>
      <div className="space-y-4">
        {isLoading && <p>Loading jewelry items...</p>}
        {error && <p>Error loading jewelry items</p>}
        {jewelry?.map((item: { id: number; name: string }) => (
          <div key={item.id} className="flex justify-between items-center">
            <span>{item.name}</span>
            <Button onClick={() => handleDeleteJewelry(item.id)} isLoading={deleteJewelryMutation.isPending}>
              Delete
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ManageJewelry;
