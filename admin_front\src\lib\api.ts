import axios, { AxiosInstance, AxiosError } from 'axios';
import toast from 'react-hot-toast';

// Get API base URL from environment variables with fallback
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:8000/api';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 20000, // Increased timeout to 20 seconds
});

// Token management
class TokenManager {
  private static readonly ACCESS_TOKEN_KEY = 'access_token';
  private static readonly REFRESH_TOKEN_KEY = 'refresh_token';

  static getAccessToken(): string | null {
    return localStorage.getItem(this.ACCESS_TOKEN_KEY);
  }

  static getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  static setTokens(accessToken: string, refreshToken: string): void {
    localStorage.setItem(this.ACCESS_TOKEN_KEY, accessToken);
    localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
  }

  static clearTokens(): void {
    localStorage.removeItem(this.ACCESS_TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
  }

  static isAuthenticated(): boolean {
    return !!this.getAccessToken();
  }
}

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = TokenManager.getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh and errors
api.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const original = error.config as any;
    
    if (error.response?.status === 401 && original && !original._retry) {
      original._retry = true;
      
      const refreshToken = TokenManager.getRefreshToken();      if (refreshToken) {
        try {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {}, {
            headers: {
              'Authorization': `Bearer ${refreshToken}`
            }
          });
          
          const { access_token, refresh_token: newRefreshToken } = response.data;
          TokenManager.setTokens(access_token, newRefreshToken || refreshToken);
          
          // Retry original request with new token
          original.headers.Authorization = `Bearer ${access_token}`;
          return api(original);
        } catch (refreshError) {
          // Refresh failed, logout user
          TokenManager.clearTokens();
          window.location.href = '/login';
          return Promise.reject(refreshError);
        }
      } else {
        // No refresh token, logout user
        TokenManager.clearTokens();
        window.location.href = '/login';
      }
    }
    
    // Show error toast for non-auth errors
    if (error.response?.status !== 401) {
      const message = (error.response?.data as any)?.message || 
                     (error.response?.data as any)?.detail || 
                     error.message || 
                     'An error occurred';
      toast.error(message);
    }
    if (error.response?.status === 500) {
      toast.error('Internal server error. Please try again later.');
    }
    
    return Promise.reject(error);
  }
);

// Retry logic for handling temporary network issues
api.interceptors.response.use(
  response => response,
  async error => {
    const config = error.config;
    if (!config || !config.retry) return Promise.reject(error);

    config.retryCount = config.retryCount || 0;
    if (config.retryCount >= config.retry) return Promise.reject(error);

    config.retryCount += 1;
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retrying
    return api(config);
  }
);

// API wrapper with additional error handling and logging
class ApiService {
  private static logRequest(method: string, url: string, data?: any) {
    if (import.meta.env.DEV) {
      console.log(`API ${method.toUpperCase()} ${url}`, data || '');
    }
  }

  private static handleError(error: any, context: string) {
    console.error(`API Error in ${context}:`, error);
    throw error;
  }

  // Generic HTTP methods
  static async get(url: string, config?: any) {
    try {
      this.logRequest('get', url);
      const response = await api.get(url, config);
      return response;
    } catch (error) {
      this.handleError(error, `GET ${url}`);
    }
  }

  static async post(url: string, data?: any, config?: any) {
    try {
      this.logRequest('post', url, data);
      const response = await api.post(url, data, config);
      return response;
    } catch (error) {
      this.handleError(error, `POST ${url}`);
    }
  }

  static async put(url: string, data?: any, config?: any) {
    try {
      this.logRequest('put', url, data);
      const response = await api.put(url, data, config);
      return response;
    } catch (error) {
      this.handleError(error, `PUT ${url}`);
    }
  }

  static async delete(url: string, config?: any) {
    try {
      this.logRequest('delete', url);
      const response = await api.delete(url, config);
      return response;
    } catch (error) {
      this.handleError(error, `DELETE ${url}`);
    }
  }

  static async patch(url: string, data?: any, config?: any) {
    try {
      this.logRequest('patch', url, data);
      const response = await api.patch(url, data, config);
      return response;
    } catch (error) {
      this.handleError(error, `PATCH ${url}`);
    }
  }

  // Authentication endpoints
  static auth = {
    login: (credentials: { email: string; password: string }) =>
      this.post('/auth/login', credentials),
    
    logout: () =>
      this.post('/auth/logout'),
    
    me: () =>
      this.get('/auth/me'),
    
    refresh: (refreshToken: string) =>
      this.post('/auth/refresh', { refresh_token: refreshToken }),
  };
  // Dashboard endpoints
  static dashboard = {
    summary: () =>
      this.get('/dashboard/summary'),
    
    sales: (params?: { period?: string }) =>
      this.get('/dashboard/sales', { params }),
    
    stock: () =>
      this.get('/dashboard/stock'),
    
    activity: () =>
      this.get('/dashboard/activity'),
  };  // Diamond endpoints
  static diamonds = {
    list: (params?: { 
      page?: number; 
      limit?: number; 
      search?: string; 
      shape?: string; 
      status?: string; 
    }) =>
      this.get('/diamonds', { params }),
    
    get: (id: number) =>
      this.get(`/diamonds/${id}`),
    
    create: (data: any) =>
      this.post('/diamonds', data),
    
    update: (id: number, data: any) =>
      this.put(`/diamonds/${id}`, data),
      delete: (id: number) =>
      this.delete(`/diamonds/${id}`),
    
    deductStock: (id: number, quantity: number) =>
      this.patch(`/diamonds/${id}/deduct`, { quantity }),
    
    assignToManufacturing: (diamondId: number, manufacturingId: number) =>
      this.patch(`/diamonds/${diamondId}/assign-manufacturing/${manufacturingId}`),
    
    assignToJewelry: (diamondId: number, jewelryId: number) =>
      this.patch(`/diamonds/${diamondId}/assign-jewelry/${jewelryId}`),
  };

  // Shape endpoints
  static shapes = {
    list: async () => {
      const response = await this.get('/shapes');
      return response?.data ?? null;
    },
    create: async (data: any) => {
      const response = await this.post('/shapes', data);
      return response?.data ?? null;
    },
    delete: async (id: number) => {
      const response = await this.delete(`/shapes/${id}`);
      return response?.data ?? null;
    },
  };
  // Vendor endpoints
  static vendors = {
    list: (params?: { page?: number; limit?: number; search?: string }) =>
      this.get('/vendors', { params }),
    
    get: (id: number) =>
      this.get(`/vendors/${id}`),
    
    create: (data: any) =>
      this.post('/vendors', data),
    
    update: (id: number, data: any) =>
      this.put(`/vendors/${id}`, data),
    
    delete: (id: number) =>
      this.delete(`/vendors/${id}`),
  };

  // Manufacturing endpoints
  static manufacturing = {
    list: (params?: { page?: number; limit?: number; status?: string; vendor_id?: string }) =>
      this.get('/manufacturing', { params }),
    
    get: (id: number) =>
      this.get(`/manufacturing/${id}`),
    
    create: (data: any) =>
      this.post('/manufacturing', data),
    
    update: (id: number, data: any) =>
      this.put(`/manufacturing/${id}`, data),
    
    returnToStock: (id: number) =>
      this.post(`/manufacturing/${id}/return_to_stock`),
  };

  // Jewelry endpoints
  static jewelry = {
    list: (params?: { page?: number; limit?: number; search?: string; status?: string }) =>
      this.get('/jewelry', { params }),
    
    get: (id: number) =>
      this.get(`/jewelry/${id}`),
    
    create: (data: any) =>
      this.post('/jewelry', data),
    
    update: (id: number, data: any) =>
      this.put(`/jewelry/${id}`, data),
    
    delete: (id: number) =>
      this.delete(`/jewelry/${id}`),
    
    uploadImage: (id: number, file: File) => {
      const formData = new FormData();
      formData.append('image', file);
      return this.post(`/jewelry/${id}/image`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
    },
  };

  // Sales endpoints
  static sales = {
    list: (params?: { 
      page?: number; 
      limit?: number; 
      date_from?: string; 
      date_to?: string; 
      status?: string; 
    }) =>
      this.get('/sales', { params }),
    
    get: (id: number) =>
      this.get(`/sales/${id}`),
    
    create: (data: any) =>
      this.post('/sales', data),
    
    update: (id: number, data: any) =>
      this.put(`/sales/${id}`, data),
    
    updatePaymentStatus: (id: number) =>
      this.post(`/sales/${id}/mark-paid`),
  };

  // User endpoints
  static users = {
    list: () => this.get('/users'),
    delete: (id: number) => this.delete(`/users/${id}`),
  };
}

export { ApiService as api, TokenManager };