from flask_restx import Namespace, Resource, fields
from flask import request
from app.models.manufacturing import ManufacturingRequest, manufacturing_diamonds
from app.models.diamond import Diamond
from app import db
from app.utils.decorators import token_required
from datetime import datetime

manufacturing_ns = Namespace('manufacturing', description='Manufacturing operations', path='/manufacturing')

# Swagger models
diamond_link_model = manufacturing_ns.model('ManufacturingDiamond', {
    'diamond_id': fields.Integer(required=True),
    'quantity': fields.Integer(required=True)
})

manufacturing_model = manufacturing_ns.model('Manufacturing', {
    'id': fields.Integer(readOnly=True),
    'vendor_id': fields.Integer(required=True),
    'sent_date': fields.String,
    'expected_return_date': fields.String,
    'return_date': fields.String,
    'status': fields.String,
    'diamonds': fields.List(fields.Nested(diamond_link_model)),
    'created_at': fields.String,
    'vendor': fields.Nested(manufacturing_ns.model('VendorShort', {
        'id': fields.Integer,
        'name': fields.String
    }))
})

create_manufacturing_model = manufacturing_ns.model('CreateManufacturing', {
    'vendor_id': fields.Integer(required=True),
    'sent_date': fields.String,
    'expected_return_date': fields.String,
    'diamonds': fields.List(fields.Nested(diamond_link_model))
})

error_model = manufacturing_ns.model('ManufacturingError', {
    'status': fields.String(description='Error status'),
    'message': fields.String(description='Error message'),
    'status_code': fields.Integer(description='HTTP status code')
})

def manufacturing_to_dict(r):
    return {
        'id': r.id,
        'vendor_id': r.vendor_id,
        'vendor': {
            'id': r.vendor.id,
            'name': r.vendor.name,
            'gst_number': r.vendor.gst_number,
            'contact_number': r.vendor.contact_number,
            'address': r.vendor.address
        } if r.vendor else None,
        'sent_date': r.sent_date.isoformat() if r.sent_date else None,
        'expected_return_date': r.expected_return_date.isoformat() if r.expected_return_date else None,
        'return_date': r.return_date.isoformat() if r.return_date else None,
        'status': r.status,
        'created_at': r.created_at.isoformat() if r.created_at else None,
        'diamonds': [
            {
                'diamond_id': d.id,
                'quantity': db.session.execute(manufacturing_diamonds.select().where(manufacturing_diamonds.c.manufacturing_id == r.id, manufacturing_diamonds.c.diamond_id == d.id)).first().quantity,
                'diamond': {
                    'shape': d.shape.name if d.shape else None,
                    'carat': d.carat,
                    'clarity': d.clarity,
                    'color': d.color,
                    'certificate_no': d.certificate_no,
                    'size_mm': d.size_mm,
                    'status': d.status
                }
            } for d in r.diamonds
        ]
    }

@manufacturing_ns.route('/')
class ManufacturingList(Resource):
    @manufacturing_ns.doc('list_manufacturing')
    @manufacturing_ns.marshal_list_with(manufacturing_model)
    @manufacturing_ns.response(200, 'List of manufacturing requests', [manufacturing_model])
    @manufacturing_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def get(self):
        """List all manufacturing requests (optionally filter by status/vendor_id)"""
        query = ManufacturingRequest.query
        for field in ['status', 'vendor_id']:
            value = request.args.get(field)
            if value:
                query = query.filter(getattr(ManufacturingRequest, field) == value)
        requests = query.all()
        return [manufacturing_to_dict(r) for r in requests], 200

    @manufacturing_ns.doc('create_manufacturing')
    @manufacturing_ns.expect(create_manufacturing_model)
    @manufacturing_ns.marshal_with(manufacturing_model, code=201)
    @manufacturing_ns.response(400, 'Validation error', error_model)
    @manufacturing_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def post(self):
        """Create a new manufacturing request"""
        data = request.get_json()
        try:
            # Start a transaction
            with db.session.begin_nested():
                r = ManufacturingRequest(
                    vendor_id=data['vendor_id'],
                    sent_date=datetime.strptime(data['sent_date'], '%Y-%m-%d').date() if 'sent_date' in data else None,
                    expected_return_date=datetime.strptime(data['expected_return_date'], '%Y-%m-%d').date() if 'expected_return_date' in data else None,
                    status='open'
                )
                db.session.add(r)
                db.session.flush()  # Get r.id to use in the association table

                for d_data in data.get('diamonds', []):
                    diamond_id = d_data['diamond_id']
                    quantity_to_use = d_data['quantity']

                    # Lock the diamond row for update
                    diamond = Diamond.query.with_for_update().get(diamond_id)

                    if not diamond:
                        manufacturing_ns.abort(404, f'Diamond {diamond_id} not found')
                    
                    if diamond.quantity < quantity_to_use:
                        manufacturing_ns.abort(400, f'Not enough stock for diamond {diamond.id}. Available: {diamond.quantity}, Requested: {quantity_to_use}')

                    # Deduct stock
                    diamond.quantity -= quantity_to_use
                    
                    # Add to association table
                    db.session.execute(manufacturing_diamonds.insert().values(
                        manufacturing_id=r.id, diamond_id=diamond.id, quantity=quantity_to_use
                    ))

            db.session.commit()
            return manufacturing_to_dict(r), 201
        except Exception as e:
            db.session.rollback()
            manufacturing_ns.abort(400, f'Failed to create manufacturing request: {str(e)}')

@manufacturing_ns.route('/<int:request_id>')
@manufacturing_ns.param('request_id', 'The manufacturing request identifier')
@manufacturing_ns.response(404, 'Manufacturing request not found', error_model)
class ManufacturingResource(Resource):
    @manufacturing_ns.doc('get_manufacturing')
    @manufacturing_ns.marshal_with(manufacturing_model)
    @manufacturing_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def get(self, request_id):
        """Get a manufacturing request by ID"""
        r = ManufacturingRequest.query.get_or_404(request_id)
        return manufacturing_to_dict(r), 200

@manufacturing_ns.route('/<int:request_id>/return_to_stock')
@manufacturing_ns.param('request_id', 'The manufacturing request identifier')
class ManufacturingReturnToStock(Resource):
    @manufacturing_ns.doc('return_to_stock')
    @token_required
    def post(self, request_id):
        """Return diamonds from a manufacturing request to stock and complete the request."""
        req = ManufacturingRequest.query.get_or_404(request_id)
        if req.status == 'completed':
            return {'message': 'Request already completed'}, 400

        links = db.session.execute(manufacturing_diamonds.select().where(
            manufacturing_diamonds.c.manufacturing_id == req.id)).fetchall()

        for link in links:
            diamond = Diamond.query.get(link.diamond_id)
            if diamond:
                diamond.quantity += link.quantity
                if diamond.status == 'used':
                    diamond.status = 'in_stock'

        req.status = 'completed'
        req.return_date = datetime.utcnow().date()
        db.session.commit()

        return {'message': 'Diamonds returned to stock and request completed.'}, 200

@manufacturing_ns.route('/history')
class ManufacturingHistory(Resource):
    @manufacturing_ns.doc('manufacturing_history')
    @manufacturing_ns.marshal_list_with(manufacturing_model)
    @token_required
    def get(self):
        """Get manufacturing request history (optionally filter by vendor_id and date range)"""
        query = ManufacturingRequest.query
        for field in ['vendor_id']:
            value = request.args.get(field)
            if value:
                query = query.filter(getattr(ManufacturingRequest, field) == value)
        start = request.args.get('start_date')
        end = request.args.get('end_date')
        if start:
            query = query.filter(ManufacturingRequest.sent_date >= datetime.strptime(start, '%Y-%m-%d').date())
        if end:
            query = query.filter(ManufacturingRequest.sent_date <= datetime.strptime(end, '%Y-%m-%d').date())
        requests = query.order_by(ManufacturingRequest.sent_date.desc()).all()
        return [manufacturing_to_dict(r) for r in requests]
