import React from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Plus, Trash2 } from 'lucide-react';
import { api } from '../../lib/api';
import Input from '../../components/ui/Input';
import Select from '../../components/ui/Select';
import Button from '../../components/ui/Button';
import toast from 'react-hot-toast';

interface ManufacturingFormProps {
  onSuccess: () => void;
}

interface ManufacturingFormData {
  vendor_id: string;
  sent_date: string;
  expected_return_date: string;
  diamonds: Array<{
    diamond_id: string;
    quantity: number;
  }>;
}

const ManufacturingForm: React.FC<ManufacturingFormProps> = ({ onSuccess }) => {
  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    watch
  } = useForm<ManufacturingFormData>({
    defaultValues: {
      sent_date: new Date().toISOString().split('T')[0],
      expected_return_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      diamonds: [{ diamond_id: '', quantity: 1 }]
    }
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'diamonds'
  });

  const { data: vendors } = useQuery({
    queryKey: ['vendors'],
    queryFn: async () => {
      const response = await api.vendors.list();
      return response?.data || [];
    }
  });

  const { data: diamonds } = useQuery({
    queryKey: ['diamonds', { status: 'in_stock' }],
    queryFn: async () => {
      const response = await api.diamonds.list({ status: 'in_stock' });
      return response?.data?.data || [];
    }
  });

  const mutation = useMutation({
    mutationFn: async (data: ManufacturingFormData) => {
      await api.manufacturing.create(data);
    },
    onSuccess: () => {
      toast.success('Manufacturing request created successfully');
      onSuccess();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to create manufacturing request');
    }
  });

  const onSubmit = (data: ManufacturingFormData) => {
    mutation.mutate(data);
  };

  const watchedDiamonds = watch('diamonds');

  const vendorOptions = Array.isArray(vendors)
    ? vendors.map((vendor: any) => ({
        value: vendor.id.toString(),
        label: vendor.name
      }))
    : [];

  const getDiamondOptions = (currentIndex: number) => {
    const selectedDiamondIds = watchedDiamonds
      .map((d, index) => (index !== currentIndex ? d.diamond_id : null))
      .filter(Boolean);

    if (!Array.isArray(diamonds)) return [];

    return diamonds
      .filter((d: any) => !selectedDiamondIds.includes(d.id.toString()))
      .map((diamond: any) => ({
        value: diamond.id.toString(),
        label: `${diamond.shape} - ${diamond.carat}ct (${diamond.quantity} available)`
      }));
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <Select
        label="Vendor"
        options={vendorOptions}
        {...register('vendor_id', { required: 'Vendor is required' })}
        error={errors.vendor_id?.message}
      />

      <div className="grid grid-cols-2 gap-4">
        <Input
          label="Sent Date"
          type="date"
          {...register('sent_date', { required: 'Sent date is required' })}
          error={errors.sent_date?.message}
        />

        <Input
          label="Expected Return Date"
          type="date"
          {...register('expected_return_date', { required: 'Expected return date is required' })}
          error={errors.expected_return_date?.message}
        />
      </div>

      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">Diamonds</h3>
          <Button
            type="button"
            variant="secondary"
            onClick={() => append({ diamond_id: '', quantity: 1 })}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Diamond
          </Button>
        </div>

        {fields.map((field, index) => {
          const selectedDiamond = Array.isArray(diamonds)
            ? diamonds.find((d: any) => d.id === parseInt(watchedDiamonds[index]?.diamond_id))
            : undefined;
          
          return (
            <div key={field.id} className="flex gap-4 items-end">
              <div className="flex-1">
                <Select
                  label="Diamond"
                  options={getDiamondOptions(index)}
                  {...register(`diamonds.${index}.diamond_id`, { required: 'Diamond is required' })}
                  error={errors.diamonds?.[index]?.diamond_id?.message}
                />
              </div>

              <div className="w-32">
                <Input
                  label="Quantity"
                  type="number"
                  min="1"
                  max={selectedDiamond?.quantity || 1}
                  {...register(`diamonds.${index}.quantity`, {
                    required: 'Quantity is required',
                    min: { value: 1, message: 'Minimum quantity is 1' },
                    max: { 
                      value: selectedDiamond?.quantity || 1, 
                      message: `Maximum available: ${selectedDiamond?.quantity || 1}` 
                    }
                  })}
                  error={errors.diamonds?.[index]?.quantity?.message}
                />
              </div>

              {fields.length > 1 && (
                <Button
                  type="button"
                  variant="danger"
                  onClick={() => remove(index)}
                  className="mb-1"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          );
        })}
      </div>

      <div className="flex gap-4">
        <Button
          type="submit"
          disabled={mutation.isPending}
          className="flex-1"
        >
          {mutation.isPending ? 'Creating...' : 'Create Manufacturing Request'}
        </Button>
      </div>
    </form>
  );
};

export default ManufacturingForm;