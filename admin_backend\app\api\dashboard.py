from flask_restx import Namespace, Resource, fields
from app.models.diamond import Diamond
from app.models.jewelry import JewelryItem
from app.models.sale import Sale
from app.models.manufacturing import ManufacturingRequest
from app.utils.decorators import token_required
from app import db
from sqlalchemy import func

# Define namespace

dashboard_ns = Namespace('dashboard', description='Dashboard statistics and summaries', path='/dashboard')

summary_model = dashboard_ns.model('DashboardSummary', {
    'diamonds_in_stock': fields.Integer,
    'jewelry_in_stock': fields.Integer,
    'jewelry_sold': fields.Integer,
    'total_sales': fields.Integer,
    'open_manufacturing': fields.Integer,
    'completed_manufacturing': fields.Integer,
    'total_inventory_value': fields.Float  # Added field
})

sales_stats_model = dashboard_ns.model('SalesStats', {
    'total_sales': fields.Integer,
    'paid_sales': fields.Integer,
    'unpaid_sales': fields.Integer
})

stock_stats_model = dashboard_ns.model('StockStats', {
    'diamonds_in_stock': fields.Integer,
    'jewelry_in_stock': fields.Integer
})

error_model = dashboard_ns.model('DashboardError', {
    'status': fields.String(description='Error status'),
    'message': fields.String(description='Error message'),
    'status_code': fields.Integer(description='HTTP status code')
})

@dashboard_ns.route('/')
class Dashboard(Resource):
    @dashboard_ns.doc('dashboard_main')
    @dashboard_ns.marshal_with(summary_model)
    @dashboard_ns.response(200, 'Dashboard data', summary_model)
    @dashboard_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def get(self):
        """Get main dashboard data"""
        try:
            diamonds_in_stock = Diamond.query.filter_by(status='in_stock').count()
            jewelry_in_stock = JewelryItem.query.filter_by(status='in_stock').count()
            jewelry_sold = JewelryItem.query.filter_by(status='sold').count()
            total_sales = Sale.query.count()
            open_manufacturing = ManufacturingRequest.query.filter_by(status='open').count()
            completed_manufacturing = ManufacturingRequest.query.filter_by(status='completed').count()

            # Calculate total inventory value
            diamond_value = db.session.query(func.sum(Diamond.cost_price)).filter_by(status='in_stock').scalar() or 0
            jewelry_value = db.session.query(func.sum(JewelryItem.cost_price)).filter_by(status='in_stock').scalar() or 0
            total_inventory_value = diamond_value + jewelry_value

            return {
                'diamonds_in_stock': diamonds_in_stock,
                'jewelry_in_stock': jewelry_in_stock,
                'jewelry_sold': jewelry_sold,
                'total_sales': total_sales,
                'open_manufacturing': open_manufacturing,
                'completed_manufacturing': completed_manufacturing,
                'total_inventory_value': total_inventory_value
            }
        except Exception as e:
            dashboard_ns.abort(500, f'Error retrieving dashboard data: {str(e)}')

@dashboard_ns.route('/summary')
class DashboardSummary(Resource):
    @dashboard_ns.doc('dashboard_summary')
    @dashboard_ns.marshal_with(summary_model)
    @dashboard_ns.response(200, 'Dashboard summary', summary_model)
    @dashboard_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def get(self):
        try:
            diamonds_in_stock = Diamond.query.filter_by(status='in_stock').count()
            jewelry_in_stock = JewelryItem.query.filter_by(status='in_stock').count()
            jewelry_sold = JewelryItem.query.filter_by(status='sold').count()
            total_sales = Sale.query.count()
            open_manufacturing = ManufacturingRequest.query.filter_by(status='open').count()
            completed_manufacturing = ManufacturingRequest.query.filter_by(status='completed').count()
            diamond_value = db.session.query(func.sum(Diamond.carat)).filter(Diamond.status=='in_stock').scalar() or 0
            jewelry_value = db.session.query(func.sum(JewelryItem.gross_weight)).filter(JewelryItem.status=='in_stock').scalar() or 0
            total_inventory_value = float(diamond_value) + float(jewelry_value)
            return {
                'diamonds_in_stock': diamonds_in_stock,
                'jewelry_in_stock': jewelry_in_stock,
                'jewelry_sold': jewelry_sold,
                'total_sales': total_sales,
                'open_manufacturing': open_manufacturing,
                'completed_manufacturing': completed_manufacturing,
                'total_inventory_value': total_inventory_value
            }, 200
        except Exception as e:
            dashboard_ns.abort(500, f'Failed to get dashboard summary: {str(e)}')

# Add recent activity endpoint
activity_model = dashboard_ns.model('DashboardActivity', {
    'type': fields.String,
    'description': fields.String,
    'date': fields.String
})

@dashboard_ns.route('/activity')
class DashboardActivity(Resource):
    @dashboard_ns.doc('dashboard_activity')
    @dashboard_ns.marshal_list_with(activity_model)
    @dashboard_ns.response(200, 'Recent activity', [activity_model])
    @dashboard_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def get(self):
        try:
            # Get recent sales
            sales = Sale.query.order_by(Sale.sale_date.desc()).limit(5).all()
            sales_activity = [{
                'type': 'sale',
                'description': f"Sold {s.jewelry_item.name} to {s.customer_name} (Invoice: {s.invoice_no})",
                'date': s.sale_date.isoformat()
            } for s in sales]
            # Get recent manufacturing
            manufacturing = ManufacturingRequest.query.order_by(ManufacturingRequest.sent_date.desc()).limit(5).all()
            manufacturing_activity = [{
                'type': 'manufacturing',
                'description': f"Manufacturing request sent to vendor {m.vendor_id}",
                'date': m.sent_date.isoformat()
            } for m in manufacturing]
            # Get recent jewelry added
            jewelry = JewelryItem.query.order_by(JewelryItem.received_date.desc()).limit(5).all()
            jewelry_activity = [{
                'type': 'jewelry',
                'description': f"Jewelry item {j.name} received (Design: {j.design_code})",
                'date': j.received_date.isoformat()
            } for j in jewelry]
            # Combine and sort by date (descending)
            all_activity = sales_activity + manufacturing_activity + jewelry_activity
            all_activity.sort(key=lambda x: x['date'], reverse=True)
            return all_activity[:10], 200
        except Exception as e:
            dashboard_ns.abort(400, f'Failed to get recent activity: {str(e)}')

@dashboard_ns.route('/sales')
class DashboardSalesStats(Resource):
    @dashboard_ns.doc('dashboard_sales_stats')
    @dashboard_ns.marshal_with(sales_stats_model)
    @dashboard_ns.response(200, 'Sales statistics', sales_stats_model)
    @dashboard_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def get(self):
        """Get sales statistics"""
        # DEBUG LOGGING
        unpaid_sales = Sale.query.filter(Sale.payment_status == 'unpaid').count()
        paid_sales = Sale.query.filter(Sale.payment_status == 'paid').count()
        total_sales = Sale.query.count()
        print(f"[DASHBOARD SALES] total_sales={total_sales}, paid_sales={paid_sales}, unpaid_sales={unpaid_sales}")
        return {'total_sales': total_sales, 'paid_sales': paid_sales, 'unpaid_sales': unpaid_sales}

@dashboard_ns.route('/stock')
class DashboardStockStats(Resource):
    @dashboard_ns.doc('dashboard_stock_stats')
    @dashboard_ns.marshal_with(stock_stats_model)
    @dashboard_ns.response(200, 'Stock statistics', stock_stats_model)
    @dashboard_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def get(self):
        """Get stock statistics"""
        diamonds_in_stock = Diamond.query.filter_by(status='in_stock').count()
        jewelry_in_stock = JewelryItem.query.filter_by(status='in_stock').count()
        return {'diamonds_in_stock': diamonds_in_stock, 'jewelry_in_stock': jewelry_in_stock}
