{".class": "MypyFile", "_fullname": "app.api.sale", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "IntegrityError": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc.IntegrityError", "kind": "Gdef"}, "JewelryItem": {".class": "SymbolTableNode", "cross_ref": "app.models.jewelry.JewelryItem", "kind": "Gdef"}, "Namespace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.sale.Namespace", "name": "Namespace", "type": {".class": "AnyType", "missing_import_name": "app.api.sale.Namespace", "source_any": null, "type_of_any": 3}}}, "Resource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.sale.Resource", "name": "Resource", "type": {".class": "AnyType", "missing_import_name": "app.api.sale.Resource", "source_any": null, "type_of_any": 3}}}, "Sale": {".class": "SymbolTableNode", "cross_ref": "app.models.sale.Sale", "kind": "Gdef"}, "SaleInvoice": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.sale.SaleInvoice", "name": "SaleInvoice", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.sale.SaleInvoice", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.sale", "mro": ["app.api.sale.SaleInvoice", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sale_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.sale.SaleInvoice.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.sale.SaleInvoice.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.api.sale.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.sale.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.sale.SaleInvoice.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.sale.SaleInvoice", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SaleList": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.sale.SaleList", "name": "SaleList", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.sale.SaleList", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.sale", "mro": ["app.api.sale.SaleList", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.sale.SaleList.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.sale.SaleList.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.api.sale.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.sale.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.sale.SaleList.post", "name": "post", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.sale.SaleList.post", "name": "post", "type": {".class": "AnyType", "missing_import_name": "app.api.sale.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.sale.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.sale.SaleList.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.sale.SaleList", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SaleMarkPaid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.sale.SaleMarkPaid", "name": "SaleMarkPaid", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.sale.SaleMarkPaid", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.sale", "mro": ["app.api.sale.SaleMarkPaid", "builtins.object"], "names": {".class": "SymbolTable", "patch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sale_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.sale.SaleMarkPaid.patch", "name": "patch", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.sale.SaleMarkPaid.patch", "name": "patch", "type": {".class": "AnyType", "missing_import_name": "app.api.sale.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.sale.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.sale.SaleMarkPaid.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.sale.SaleMarkPaid", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SaleResource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.sale.SaleResource", "name": "SaleResource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.sale.SaleResource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.sale", "mro": ["app.api.sale.SaleResource", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sale_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.sale.SaleResource.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.sale.SaleResource.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.api.sale.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.sale.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.sale.SaleResource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.sale.SaleResource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.sale.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.sale.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.sale.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.sale.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.sale.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.sale.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "create_sale_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.sale.create_sale_model", "name": "create_sale_model", "type": {".class": "AnyType", "missing_import_name": "app.api.sale.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.sale.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "current_app": {".class": "SymbolTableNode", "cross_ref": "flask.globals.current_app", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "db": {".class": "SymbolTableNode", "cross_ref": "app.db", "kind": "Gdef"}, "error_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.sale.error_model", "name": "error_model", "type": {".class": "AnyType", "missing_import_name": "app.api.sale.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.sale.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "fields": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.sale.fields", "name": "fields", "type": {".class": "AnyType", "missing_import_name": "app.api.sale.fields", "source_any": null, "type_of_any": 3}}}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "make_response": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.make_response", "kind": "Gdef"}, "pisa": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.sale.pisa", "name": "pisa", "type": {".class": "AnyType", "missing_import_name": "app.api.sale.pisa", "source_any": null, "type_of_any": 3}}}, "render_template": {".class": "SymbolTableNode", "cross_ref": "flask.templating.render_template", "kind": "Gdef"}, "request": {".class": "SymbolTableNode", "cross_ref": "flask.globals.request", "kind": "Gdef"}, "sale_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.sale.sale_model", "name": "sale_model", "type": {".class": "AnyType", "missing_import_name": "app.api.sale.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.sale.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "sale_ns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.sale.sale_ns", "name": "sale_ns", "type": {".class": "AnyType", "missing_import_name": "app.api.sale.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.sale.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "sale_to_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.api.sale.sale_to_dict", "name": "sale_to_dict", "type": null}}, "token_required": {".class": "SymbolTableNode", "cross_ref": "app.utils.decorators.token_required", "kind": "Gdef"}}, "path": "E:\\admin_panel\\admin_backend\\app\\api\\sale.py"}