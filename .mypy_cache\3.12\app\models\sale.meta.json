{"data_mtime": 1753034597, "dep_lines": [1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["app", "datetime", "builtins", "_frozen_importlib", "abc", "flask_sqlalchemy", "flask_sqlalchemy.extension", "sqlalchemy", "sqlalchemy.log", "sqlalchemy.orm", "sqlalchemy.orm.base", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.relationships", "sqlalchemy.sql", "sqlalchemy.sql.cache_key", "sqlalchemy.util", "sqlalchemy.util.langhelpers", "typing"], "hash": "1fbe264d8512c427f2244a0a84d9f8856ef7c1ce", "id": "app.models.sale", "ignore_all": true, "interface_hash": "40564112da0587af6a57f7cebb212b1eec6b777c", "mtime": 1752122903, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\admin_panel\\admin_backend\\app\\models\\sale.py", "plugin_data": null, "size": 714, "suppressed": [], "version_id": "1.15.0"}