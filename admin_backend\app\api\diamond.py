# This file includes strengthened backend validation, audit logging, and error handling for production use.
from flask_restx import Namespace, Resource, fields
from flask import request
from app.models.diamond import Diamond, Shape, Manufacturing, Jewelry
from app.models.vendor import Vendor
from app import db
from app.utils.decorators import token_required, admin_required
from datetime import datetime
from sqlalchemy.exc import IntegrityError
from functools import wraps
import re
import logging

diamond_ns = Namespace('diamonds', description='Diamond Inventory', path='/')

diamond_model = diamond_ns.model('Diamond', {
    'id': fields.Integer(readOnly=True),
    'shape': fields.String(required=False),
    'shape_id': fields.Integer(required=True),

    # Basic Properties
    'carat': fields.Float(required=True),

    # 4Cs - Industry Standard
    'color': fields.String(required=True, description='Diamond color grade (D-Z)'),
    'clarity': fields.String(required=True, description='Diamond clarity grade (FL, IF, VVS1, etc.)'),
    'cut_grade': fields.String(description='Cut grade (Excellent, Very Good, etc.)'),

    # Additional Grading
    'polish': fields.String(description='Polish grade'),
    'symmetry': fields.String(description='Symmetry grade'),
    'fluorescence': fields.String(description='Fluorescence intensity'),
    'fluorescence_color': fields.String(description='Fluorescence color'),

    # Measurements
    'length_mm': fields.Float(description='Length in millimeters'),
    'width_mm': fields.Float(description='Width in millimeters'),
    'depth_mm': fields.Float(description='Depth in millimeters'),
    'depth_percent': fields.Float(description='Depth percentage'),
    'table_percent': fields.Float(description='Table percentage'),
    'girdle': fields.String(description='Girdle description'),
    'culet': fields.String(description='Culet description'),

    # Certification
    'certificate_no': fields.String(required=True),
    'certification_lab': fields.String(description='Certification laboratory'),
    'certificate_date': fields.String(description='Certificate date'),
    'certificate_url': fields.String(description='Certificate URL'),

    # Pricing
    'cost_price': fields.Float(description='Purchase cost'),
    'retail_price': fields.Float(description='Retail price'),
    'market_value': fields.Float(description='Current market value'),
    'last_valuation_date': fields.String(description='Last valuation date'),

    # Inventory
    'quantity': fields.Integer(required=True),
    'reserved_quantity': fields.Integer(description='Reserved quantity'),
    'available_quantity': fields.Integer(description='Available quantity'),
    'minimum_stock': fields.Integer(description='Minimum stock level'),

    # Status and Location
    'status': fields.String(description='Diamond status'),
    'location': fields.String(description='Storage location'),
    'notes': fields.String(description='Additional notes'),

    # Relationships
    'vendor_id': fields.Integer(required=False),
    'vendorName': fields.String(required=False),

    # Dates
    'purchase_date': fields.String,
    'created_at': fields.String(readOnly=True),
    'updated_at': fields.String(readOnly=True),

    # Calculated Fields
    'profit_margin': fields.Float(readOnly=True, description='Profit margin percentage'),
    'profit_amount': fields.Float(readOnly=True, description='Profit amount'),
    'is_low_stock': fields.Boolean(readOnly=True, description='Is below minimum stock'),

    # Legacy
    'size_mm': fields.String(description='Legacy size field')
})

error_model = diamond_ns.model('DiamondError', {
    'status': fields.String(description='Error status'),
    'message': fields.String(description='Error message'),
    'status_code': fields.Integer(description='HTTP status code')
})

diamond_update_model = diamond_ns.model('DiamondUpdate', {
    'shape_id': fields.Integer(required=False),
    'carat': fields.Float(required=False),
    'color': fields.String(required=False),
    'clarity': fields.String(required=False),
    'cut_grade': fields.String(required=False),
    'polish': fields.String(required=False),
    'symmetry': fields.String(required=False),
    'fluorescence': fields.String(required=False),
    'fluorescence_color': fields.String(required=False),
    'length_mm': fields.Float(required=False),
    'width_mm': fields.Float(required=False),
    'depth_mm': fields.Float(required=False),
    'depth_percent': fields.Float(required=False),
    'table_percent': fields.Float(required=False),
    'girdle': fields.String(required=False),
    'culet': fields.String(required=False),
    'certificate_no': fields.String(required=False),
    'certification_lab': fields.String(required=False),
    'certificate_date': fields.String(required=False),
    'certificate_url': fields.String(required=False),
    'cost_price': fields.Float(required=False),
    'retail_price': fields.Float(required=False),
    'market_value': fields.Float(required=False),
    'last_valuation_date': fields.String(required=False),
    'quantity': fields.Integer(required=False),
    'reserved_quantity': fields.Integer(required=False),
    'minimum_stock': fields.Integer(required=False),
    'status': fields.String(required=False),
    'location': fields.String(required=False),
    'notes': fields.String(required=False),
    'vendor_id': fields.Integer(required=False),
    'purchase_date': fields.String(required=False),
    'size_mm': fields.String(required=False)  # Legacy field
})

shape_model = diamond_ns.model('Shape', {
    'id': fields.Integer(readOnly=True),
    'name': fields.String(required=True)
})

def diamond_to_dict(d):
    """Convert diamond model to dictionary using the model's to_dict method."""
    return d.to_dict()

# Standardized error handling decorator
def handle_errors(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except IntegrityError:
            db.session.rollback()
            diamond_ns.abort(400, 'Database integrity error occurred.')
        except Exception as e:
            db.session.rollback()
            diamond_ns.abort(400, f'An error occurred: {str(e)}')
    return wrapper

# Setup audit logger
logger = logging.getLogger('diamond_audit')
if not logger.handlers:
    handler = logging.FileHandler('diamond_audit.log')
    formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

def validate_diamond_fields(data, is_update=False):
    """Enhanced validation for diamond fields with industry standards."""
    errors = []

    # Required fields for create
    required_fields = ['shape_id', 'carat', 'clarity', 'color', 'certificate_no']
    if not is_update:
        for field in required_fields:
            if field not in data or data[field] in [None, '']:
                errors.append(f"Missing or empty required field: {field}")

    # Carat validation
    carat = data.get('carat')
    if carat is not None:
        try:
            carat = float(carat)
            if not (0.01 <= carat <= 100):
                errors.append('Carat must be between 0.01 and 100')
        except (TypeError, ValueError):
            errors.append('Carat must be a number')

    # Color validation (GIA standard)
    color = data.get('color')
    if color and not Diamond.validate_color(color.upper()):
        errors.append(f'Color must be one of: {", ".join(Diamond.VALID_COLORS)}')

    # Clarity validation (GIA standard)
    clarity = data.get('clarity')
    if clarity and not Diamond.validate_clarity(clarity.upper()):
        errors.append(f'Clarity must be one of: {", ".join(Diamond.VALID_CLARITIES)}')

    # Cut grade validation
    cut_grade = data.get('cut_grade')
    if cut_grade and not Diamond.validate_cut_grade(cut_grade):
        errors.append(f'Cut grade must be one of: {", ".join(Diamond.VALID_CUT_GRADES)}')

    # Polish validation
    polish = data.get('polish')
    if polish and polish not in Diamond.VALID_POLISH_SYMMETRY:
        errors.append(f'Polish must be one of: {", ".join(Diamond.VALID_POLISH_SYMMETRY)}')

    # Symmetry validation
    symmetry = data.get('symmetry')
    if symmetry and symmetry not in Diamond.VALID_POLISH_SYMMETRY:
        errors.append(f'Symmetry must be one of: {", ".join(Diamond.VALID_POLISH_SYMMETRY)}')

    # Fluorescence validation
    fluorescence = data.get('fluorescence')
    if fluorescence and not Diamond.validate_fluorescence(fluorescence):
        errors.append(f'Fluorescence must be one of: {", ".join(Diamond.VALID_FLUORESCENCE)}')

    # Certification lab validation
    cert_lab = data.get('certification_lab')
    if cert_lab and not Diamond.validate_certification_lab(cert_lab):
        errors.append(f'Certification lab must be one of: {", ".join(Diamond.VALID_CERTIFICATION_LABS)}')

    # Certificate number validation
    cert = data.get('certificate_no', '')
    if cert and not re.match(r'^[a-zA-Z0-9\-]{3,50}$', cert):
        errors.append('Certificate number must be 3-50 alphanumeric characters')

    # Measurement validations
    for field in ['length_mm', 'width_mm', 'depth_mm']:
        value = data.get(field)
        if value is not None:
            try:
                value = float(value)
                if not (0.1 <= value <= 50):
                    errors.append(f'{field} must be between 0.1 and 50 mm')
            except (TypeError, ValueError):
                errors.append(f'{field} must be a number')

    # Percentage validations
    for field in ['depth_percent', 'table_percent']:
        value = data.get(field)
        if value is not None:
            try:
                value = float(value)
                if not (1 <= value <= 100):
                    errors.append(f'{field} must be between 1 and 100 percent')
            except (TypeError, ValueError):
                errors.append(f'{field} must be a number')

    # Price validations
    for field in ['cost_price', 'retail_price', 'market_value']:
        value = data.get(field)
        if value is not None:
            try:
                value = float(value)
                if value < 0:
                    errors.append(f'{field} must be non-negative')
            except (TypeError, ValueError):
                errors.append(f'{field} must be a number')

    # Quantity validations
    for field in ['quantity', 'reserved_quantity', 'minimum_stock']:
        value = data.get(field)
        if value is not None:
            try:
                value = int(value)
                if value < 0:
                    errors.append(f'{field} must be non-negative')
            except (TypeError, ValueError):
                errors.append(f'{field} must be an integer')

    # Status validation
    status = data.get('status')
    if status and status not in Diamond.VALID_STATUSES:
        errors.append(f'Status must be one of: {", ".join(Diamond.VALID_STATUSES)}')

    return errors

# Diamond list operations
@diamond_ns.route('/diamonds')
class DiamondList(Resource):
    @diamond_ns.doc('list_diamonds')
    # Removed marshal_list_with because we return a dict, not a list
    @diamond_ns.response(200, 'List of diamonds', [diamond_model])
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    @handle_errors
    def get(self):
        """Get all diamonds with comprehensive professional filtering."""
        query = Diamond.query

        # Basic filters
        for field in ['color', 'clarity', 'status', 'cut_grade', 'polish', 'symmetry',
                     'fluorescence', 'certification_lab', 'location']:
            value = request.args.get(field)
            if value:
                query = query.filter(getattr(Diamond, field) == value)

        # Handle shape filtering by shape_id or shape name
        shape_filter = request.args.get('shape')
        if shape_filter:
            if shape_filter.isdigit():
                query = query.filter(Diamond.shape_id == int(shape_filter))
            else:
                query = query.join(Shape).filter(Shape.name.ilike(f"%{shape_filter}%"))

        # Enhanced search across multiple fields
        search = request.args.get('search')
        if search:
            search = search.strip()
            query = query.join(Shape).filter(
                (Shape.name.ilike(f"%{search}%")) |
                (Diamond.clarity.ilike(f"%{search}%")) |
                (Diamond.color.ilike(f"%{search}%")) |
                (Diamond.certificate_no.ilike(f"%{search}%")) |
                (Diamond.notes.ilike(f"%{search}%")) |
                (Diamond.location.ilike(f"%{search}%"))
            )

        # Certificate number exact search
        certificate_no = request.args.get('certificate_no')
        if certificate_no:
            query = query.filter(Diamond.certificate_no.ilike(f"%{certificate_no}%"))

        # Carat range filtering
        min_carat = request.args.get('min_carat', type=float)
        max_carat = request.args.get('max_carat', type=float)
        if min_carat is not None:
            query = query.filter(Diamond.carat >= min_carat)
        if max_carat is not None:
            query = query.filter(Diamond.carat <= max_carat)

        # Price range filtering
        min_price = request.args.get('min_price', type=float)
        max_price = request.args.get('max_price', type=float)
        if min_price is not None:
            query = query.filter(Diamond.retail_price >= min_price)
        if max_price is not None:
            query = query.filter(Diamond.retail_price <= max_price)

        # Cost price range filtering
        min_cost_price = request.args.get('min_cost_price', type=float)
        max_cost_price = request.args.get('max_cost_price', type=float)
        if min_cost_price is not None:
            query = query.filter(Diamond.cost_price >= min_cost_price)
        if max_cost_price is not None:
            query = query.filter(Diamond.cost_price <= max_cost_price)

        # Quantity filtering
        min_quantity = request.args.get('min_quantity', type=int)
        if min_quantity is not None:
            query = query.filter(Diamond.quantity >= min_quantity)

        # Low stock filter
        low_stock_only = request.args.get('low_stock_only')
        if low_stock_only == 'true':
            query = query.filter(Diamond.quantity <= Diamond.minimum_stock)

        # Vendor filtering
        vendor_id = request.args.get('vendor_id', type=int)
        if vendor_id:
            query = query.filter(Diamond.vendor_id == vendor_id)

        # Enhanced sorting options
        sort_by = request.args.get('sort_by')
        sort_dir = request.args.get('sort_dir', 'asc')
        valid_sort_fields = [
            'carat', 'quantity', 'purchase_date', 'created_at', 'updated_at',
            'retail_price', 'cost_price', 'market_value', 'color', 'clarity',
            'cut_grade', 'certificate_no'
        ]

        if sort_by in valid_sort_fields:
            sort_col = getattr(Diamond, sort_by)
            if sort_dir == 'desc':
                sort_col = sort_col.desc()
            else:
                sort_col = sort_col.asc()
            query = query.order_by(sort_col)
        else:
            # Default sorting by created_at desc
            query = query.order_by(Diamond.created_at.desc())

        # Pagination
        page = request.args.get('page', default=1, type=int)
        limit = request.args.get('limit', default=20, type=int)

        # Ensure reasonable limits
        limit = min(limit, 100)  # Max 100 items per page

        pagination = query.paginate(page=page, per_page=limit, error_out=False)
        diamonds = pagination.items

        return {
            'data': [diamond_to_dict(d) for d in diamonds],
            'total': pagination.total,
            'page': page,
            'limit': limit,
            'pages': pagination.pages
        }, 200

    @diamond_ns.doc('create_diamond')
    @diamond_ns.expect(diamond_model, validate=False)
    @diamond_ns.marshal_with(diamond_model, code=201)
    @diamond_ns.response(400, 'Validation error', error_model)
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    @handle_errors
    def post(self):
        """Create a new diamond."""
        data = request.json
        try:
            # Validate fields
            errors = validate_diamond_fields(data)
            if errors:
                diamond_ns.abort(400, '; '.join(errors))
            # Convert `*` to `x` in `size_mm`
            if 'size_mm' in data and '*' in data['size_mm']:
                data['size_mm'] = data['size_mm'].replace('*', 'x')
            # Pre-check for duplicate certificate_no
            existing_diamond = Diamond.query.filter_by(certificate_no=data.get('certificate_no')).first()
            if existing_diamond:
                diamond_ns.abort(409, f"Diamond with certificate_no '{data['certificate_no']}' already exists.")
            # Vendor validation
            vendor_id = data.get('vendor_id')
            if vendor_id == '' or vendor_id is None:
                vendor_id = None
            vendor = None
            if vendor_id:
                vendor = Vendor.query.get(vendor_id)
                if not vendor:
                    diamond_ns.abort(400, f"Vendor with id {vendor_id} does not exist.")
            # Create diamond
            d = Diamond(
                shape_id=data['shape_id'],
                size_mm=data['size_mm'],
                carat=data['carat'],
                clarity=data['clarity'],
                color=data['color'],
                certificate_no=data['certificate_no'],
                quantity=data.get('quantity', 1),
                purchase_date=datetime.strptime(data['purchase_date'], '%Y-%m-%d').date() if 'purchase_date' in data else None,
                status=data.get('status', 'in_stock'),
                vendor_id=data.get('vendor_id')
            )
            db.session.add(d)
            db.session.commit()
            # Audit log
            logger.info(f"user=API action=CREATE diamond_id={d.id} cert={d.certificate_no}")
            return diamond_to_dict(d), 201
        except IntegrityError:
            db.session.rollback()
            diamond_ns.abort(400, 'Database integrity error occurred.')
        except Exception as e:
            db.session.rollback()
            diamond_ns.abort(400, f'An error occurred: {str(e)}')

# Consolidated Diamond CRUD operations
@diamond_ns.route('/diamonds/<int:diamond_id>')
class DiamondCRUD(Resource):
    @diamond_ns.doc('get_diamond')
    @diamond_ns.marshal_with(diamond_model)
    @diamond_ns.response(200, 'Diamond details', diamond_model)
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @diamond_ns.response(404, 'Diamond not found', error_model)
    @token_required
    @handle_errors
    def get(self, diamond_id):
        """Get diamond by ID."""
        d = Diamond.query.get_or_404(diamond_id)
        return diamond_to_dict(d), 200

    @diamond_ns.doc('update_diamond')
    @diamond_ns.expect(diamond_update_model, validate=True)
    @diamond_ns.marshal_with(diamond_model)
    @diamond_ns.response(400, 'Validation error', error_model)
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    @handle_errors
    def put(self, diamond_id):
        d = Diamond.query.get_or_404(diamond_id)
        data = request.json
        try:
            errors = validate_diamond_fields(data, is_update=True)
            if errors:
                diamond_ns.abort(400, '; '.join(errors))
            for field in ['shape_id', 'size_mm', 'carat', 'clarity', 'color', 'certificate_no', 'quantity', 'status']:
                if field in data:
                    setattr(d, field, data[field])
            if 'purchase_date' in data:
                d.purchase_date = datetime.strptime(data['purchase_date'], '%Y-%m-%d').date()
            # Vendor validation
            if 'vendor_id' in data:
                vendor = Vendor.query.get(data['vendor_id'])
                if not vendor:
                    diamond_ns.abort(400, f"Vendor with id {data['vendor_id']} does not exist.")
                d.vendor_id = data['vendor_id']
            db.session.commit()
            # Audit log
            logger.info(f"user=API action=UPDATE diamond_id={d.id} cert={d.certificate_no}")
            return diamond_to_dict(d), 200
        except IntegrityError:
            db.session.rollback()
            diamond_ns.abort(400, 'Database integrity error occurred.')
        except ValueError as ve:
            db.session.rollback()
            diamond_ns.abort(400, f'Value error: {str(ve)}')
        except KeyError as ke:
            db.session.rollback()
            diamond_ns.abort(400, f'Key error: {str(ke)}')
        except Exception as e:
            db.session.rollback()
            diamond_ns.abort(400, f'An unexpected error occurred: {str(e)}')

    @diamond_ns.doc('delete_diamond')
    @diamond_ns.response(200, 'Diamond deleted')
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    @admin_required
    @handle_errors
    def delete(self, diamond_id):
        d = Diamond.query.get_or_404(diamond_id)
        try:
            db.session.delete(d)
            db.session.commit()
            # Audit log
            logger.info(f"user=API action=DELETE diamond_id={d.id} cert={d.certificate_no}")
            return {'message': 'Diamond deleted'}, 200
        except Exception as e:
            db.session.rollback()
            diamond_ns.abort(400, f'An error occurred: {str(e)}')

@diamond_ns.route('/diamonds/<int:diamond_id>/deduct')
@diamond_ns.response(404, 'Diamond not found', error_model)
class DiamondDeduct(Resource):
    @diamond_ns.doc('deduct_diamond_quantity')
    @diamond_ns.expect(diamond_ns.model('DeductQuantity', {
        'quantity': fields.Integer(required=True)
    }), validate=True)
    @diamond_ns.marshal_with(diamond_model)
    @diamond_ns.response(400, 'Not enough quantity', error_model)
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    @handle_errors
    def patch(self, diamond_id):
        d = Diamond.query.get_or_404(diamond_id)
        data = request.json
        qty = data.get('quantity')

        if not isinstance(qty, int) or qty <= 0:
            diamond_ns.abort(400, 'Quantity must be a positive integer.')

        if d.quantity < qty:
            diamond_ns.abort(400, f'Not enough stock. Available: {d.quantity}, Requested: {qty}')
            
        d.quantity -= qty
        if d.quantity == 0:
            d.status = 'used'
        db.session.commit()
        return diamond_to_dict(d), 200

# Shape list operations
@diamond_ns.route('/shapes')
class ShapeList(Resource):
    @diamond_ns.doc('list_shapes')
    @diamond_ns.marshal_list_with(shape_model)
    @diamond_ns.response(200, 'List of shapes', [shape_model])
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    @handle_errors
    def get(self):
        """Get all shapes."""
        shapes = Shape.query.all()
        return [{'id': s.id, 'name': s.name} for s in shapes]

    @diamond_ns.doc('create_shape')
    @diamond_ns.expect(shape_model, validate=True)
    @diamond_ns.marshal_with(shape_model, code=201)
    @diamond_ns.response(400, 'Validation error', error_model)
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    @handle_errors
    def post(self):
        """Create a new shape."""
        data = request.json
        # Normalize name: trim and capitalize first letter
        name = data['name'].strip().capitalize()
        # Check for existing shape (case-insensitive)
        existing = Shape.query.filter(db.func.lower(Shape.name) == name.lower()).first()
        if existing:
            diamond_ns.abort(409, 'Shape name already exists.')
        shape = Shape(name=name)
        db.session.add(shape)
        db.session.commit()
        return {'id': shape.id, 'name': shape.name}, 201

# Consolidated Shape Management
@diamond_ns.route('/shapes/<int:shape_id>')
class ShapeCRUD(Resource):
    @diamond_ns.doc('get_shape')
    @diamond_ns.marshal_with(shape_model)
    @diamond_ns.response(200, 'Shape details', shape_model)
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @diamond_ns.response(404, 'Shape not found', error_model)
    @token_required
    @handle_errors
    def get(self, shape_id):
        """Get shape by ID."""
        shape = Shape.query.get_or_404(shape_id)
        return {'id': shape.id, 'name': shape.name}, 200

    @diamond_ns.doc('delete_shape')
    @diamond_ns.response(204, 'Shape deleted')
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    @admin_required
    @handle_errors
    def delete(self, shape_id):
        shape = Shape.query.get_or_404(shape_id)
        db.session.delete(shape)
        db.session.commit()
        return '', 204

@diamond_ns.route('/manufacturing-types')
class ManufacturingTypesList(Resource):
    @diamond_ns.doc('list_manufacturing_types')
    def get(self):
        """List all manufacturing process types"""
        manufacturing = Manufacturing.query.all()
        return [{'id': m.id, 'name': m.name} for m in manufacturing]

    @diamond_ns.doc('create_manufacturing_type')
    def post(self):
        """Create a new manufacturing process type"""
        data = request.json
        m = Manufacturing(name=data['name'])
        db.session.add(m)
        db.session.commit()
        return {'id': m.id, 'name': m.name}, 201

@diamond_ns.route('/jewelry')
class JewelryList(Resource):
    @diamond_ns.doc('list_jewelry')
    def get(self):
        """List all jewelry items"""
        jewelry = Jewelry.query.all()
        return [{'id': j.id, 'name': j.name} for j in jewelry]

    @diamond_ns.doc('create_jewelry')
    def post(self):
        """Create a new jewelry item"""
        data = request.json
        j = Jewelry(name=data['name'])
        db.session.add(j)
        db.session.commit()
        return {'id': j.id, 'name': j.name}, 201

@diamond_ns.route('/diamonds/<int:diamond_id>/assign-manufacturing/<int:manufacturing_id>')
class AssignManufacturing(Resource):
    @diamond_ns.doc('assign_manufacturing')
    def patch(self, diamond_id, manufacturing_id):
        """Assign a diamond to a manufacturing process"""
        diamond = Diamond.query.get_or_404(diamond_id)
        manufacturing = Manufacturing.query.get_or_404(manufacturing_id)
        diamond.manufacturing_id = manufacturing.id
        db.session.commit()
        return {'message': 'Diamond assigned to manufacturing'}, 200

@diamond_ns.route('/diamonds/<int:diamond_id>/assign-jewelry/<int:jewelry_id>')
class AssignJewelry(Resource):
    @diamond_ns.doc('assign_jewelry')
    def patch(self, diamond_id, jewelry_id):
        """Assign a diamond to a jewelry item"""
        diamond = Diamond.query.get_or_404(diamond_id)
        jewelry = Jewelry.query.get_or_404(jewelry_id)
        diamond.jewelry_id = jewelry.id
        db.session.commit()
        return {'message': 'Diamond assigned to jewelry'}, 200
