# This file includes strengthened backend validation, audit logging, and error handling for production use.
from flask_restx import Namespace, Resource, fields
from flask import request
from app.models.diamond import Diamond, Shape, Manufacturing, Jewelry
from app.models.vendor import Vendor
from app import db
from app.utils.decorators import token_required, admin_required
from datetime import datetime
from sqlalchemy.exc import IntegrityError
from functools import wraps
import re
import logging

diamond_ns = Namespace('diamonds', description='Diamond Inventory', path='/')

diamond_model = diamond_ns.model('Diamond', {
    'id': fields.Integer(readOnly=True),
    'shape': fields.String(required=False),
    'shape_id': fields.Integer(required=True),
    'size_mm': fields.String(required=True),  # Updated to String to handle both formats
    'carat': fields.Float(required=True),
    'clarity': fields.String(required=True),
    'color': fields.String(required=True),
    'certificate_no': fields.String(required=True),
    'quantity': fields.Integer(required=True),
    'purchase_date': fields.String,
    'status': fields.String,
    'vendorName': fields.String(required=False),
    'vendor_id': fields.Integer(required=False)
})

error_model = diamond_ns.model('DiamondError', {
    'status': fields.String(description='Error status'),
    'message': fields.String(description='Error message'),
    'status_code': fields.Integer(description='HTTP status code')
})

diamond_update_model = diamond_ns.model('DiamondUpdate', {
    'shape': fields.String(required=False),
    'shape_id': fields.Integer(required=False),
    'size_mm': fields.String(required=False),  # Updated to String to handle both formats
    'carat': fields.Float(required=False),
    'clarity': fields.String(required=False),
    'color': fields.String(required=False),
    'certificate_no': fields.String(required=False),
    'quantity': fields.Integer(required=False),
    'purchase_date': fields.String(required=False),
    'status': fields.String(required=False)
})

shape_model = diamond_ns.model('Shape', {
    'id': fields.Integer(readOnly=True),
    'name': fields.String(required=True)
})

def diamond_to_dict(d):
    return {
        'id': d.id,
        'shape': d.shape.name if d.shape else None,
        'size_mm': d.size_mm if '*' not in d.size_mm else d.size_mm,  # Format dimensions for frontend
        'carat': d.carat,
        'clarity': d.clarity,
        'color': d.color,
        'certificate_no': d.certificate_no,
        'quantity': d.quantity,
        'purchase_date': d.purchase_date.isoformat() if d.purchase_date else None,
        'status' : d.status,
        'vendorName': d.vendor.name if d.vendor else None,
        'vendor_id': d.vendor_id if d.vendor_id else None,
    }

# Standardized error handling decorator
def handle_errors(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except IntegrityError:
            db.session.rollback()
            diamond_ns.abort(400, 'Database integrity error occurred.')
        except Exception as e:
            db.session.rollback()
            diamond_ns.abort(400, f'An error occurred: {str(e)}')
    return wrapper

# Setup audit logger
logger = logging.getLogger('diamond_audit')
if not logger.handlers:
    handler = logging.FileHandler('diamond_audit.log')
    formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

def validate_diamond_fields(data, is_update=False):
    errors = []
    # Required fields for create
    required_fields = ['shape_id', 'size_mm', 'carat', 'clarity', 'color', 'certificate_no', 'purchase_date']
    if not is_update:
        for field in required_fields:
            if field not in data or data[field] in [None, '']:
                errors.append(f"Missing or empty required field: {field}")
    # Carat
    carat = data.get('carat')
    try:
        carat = float(carat)
        if not (0.01 <= carat <= 100):
            errors.append('Carat must be between 0.01 and 100')
    except (TypeError, ValueError):
        errors.append('Carat must be a number')
    # Size format
    size_mm = data.get('size_mm', '')
    if not re.match(r'^\d{1,2}(\.\d{1,2})?(\*\d{1,2}(\.\d{1,2})?)?$', size_mm):
        errors.append('Size must be a valid format (e.g., 6.50 or 5.50*7.90)')
    # Certificate number
    cert = data.get('certificate_no', '')
    if not re.match(r'^[a-zA-Z0-9\-]{3,30}$', cert):
        errors.append('Certificate number must be 3-30 alphanumeric characters')
    # Quantity
    try:
        quantity = int(data.get('quantity', 1))
        if quantity <= 0:
            errors.append('Quantity must be greater than 0')
    except (TypeError, ValueError):
        errors.append('Quantity must be an integer')
    return errors

# Diamond list operations
@diamond_ns.route('/diamonds')
class DiamondList(Resource):
    @diamond_ns.doc('list_diamonds')
    # Removed marshal_list_with because we return a dict, not a list
    @diamond_ns.response(200, 'List of diamonds', [diamond_model])
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    @handle_errors
    def get(self):
        """Get all diamonds with optional filtering."""
        # Filtering logic for listing diamonds
        query = Diamond.query
        for field in ['clarity', 'color', 'status', 'certificate_no']:
            value = request.args.get(field)
            if value:
                query = query.filter(getattr(Diamond, field) == value)
        
        # Handle shape filtering by shape_id or shape name
        shape_filter = request.args.get('shape')
        if shape_filter:
            if shape_filter.isdigit():
                query = query.filter(Diamond.shape_id == int(shape_filter))
            else:
                query = query.join(Shape).filter(Shape.name.ilike(f"%{shape_filter}%"))
        
        search = request.args.get('search')
        if search:
            search = search.strip()
            query = query.join(Shape).filter(
                (Shape.name.ilike(f"%{search}%")) |
                (Diamond.clarity.ilike(f"%{search}%")) |
                (Diamond.color.ilike(f"%{search}%")) |
                (Diamond.certificate_no.ilike(f"%{search}%"))
            )
        min_carat = request.args.get('min_carat', type=float)
        max_carat = request.args.get('max_carat', type=float)
        if min_carat is not None:
            query = query.filter(Diamond.carat >= min_carat)
        if max_carat is not None:
            query = query.filter(Diamond.carat <= max_carat)
        vendor_id = request.args.get('vendor_id', type=int)
        if vendor_id:
            query = query.filter(Diamond.vendor_id == vendor_id)
        # Sorting
        sort_by = request.args.get('sort_by')
        sort_dir = request.args.get('sort_dir', 'asc')
        if sort_by in ['carat', 'quantity', 'purchase_date']:
            sort_col = getattr(Diamond, sort_by)
            if sort_dir == 'desc':
                sort_col = sort_col.desc()
            else:
                sort_col = sort_col.asc()
            query = query.order_by(sort_col)
        # Pagination
        page = request.args.get('page', default=1, type=int)
        limit = request.args.get('limit', default=20, type=int)
        pagination = query.paginate(page=page, per_page=limit, error_out=False)
        diamonds = pagination.items
        return {
            'data': [diamond_to_dict(d) for d in diamonds],
            'total': pagination.total,
            'page': page,
            'limit': limit
        }, 200

    @diamond_ns.doc('create_diamond')
    @diamond_ns.expect(diamond_model, validate=False)
    @diamond_ns.marshal_with(diamond_model, code=201)
    @diamond_ns.response(400, 'Validation error', error_model)
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    @handle_errors
    def post(self):
        """Create a new diamond."""
        data = request.json
        try:
            # Validate fields
            errors = validate_diamond_fields(data)
            if errors:
                diamond_ns.abort(400, '; '.join(errors))
            # Convert `*` to `x` in `size_mm`
            if 'size_mm' in data and '*' in data['size_mm']:
                data['size_mm'] = data['size_mm'].replace('*', 'x')
            # Pre-check for duplicate certificate_no
            existing_diamond = Diamond.query.filter_by(certificate_no=data.get('certificate_no')).first()
            if existing_diamond:
                diamond_ns.abort(409, f"Diamond with certificate_no '{data['certificate_no']}' already exists.")
            # Vendor validation
            vendor_id = data.get('vendor_id')
            if vendor_id == '' or vendor_id is None:
                vendor_id = None
            vendor = None
            if vendor_id:
                vendor = Vendor.query.get(vendor_id)
                if not vendor:
                    diamond_ns.abort(400, f"Vendor with id {vendor_id} does not exist.")
            # Create diamond
            d = Diamond(
                shape_id=data['shape_id'],
                size_mm=data['size_mm'],
                carat=data['carat'],
                clarity=data['clarity'],
                color=data['color'],
                certificate_no=data['certificate_no'],
                quantity=data.get('quantity', 1),
                purchase_date=datetime.strptime(data['purchase_date'], '%Y-%m-%d').date() if 'purchase_date' in data else None,
                status=data.get('status', 'in_stock'),
                vendor_id=data.get('vendor_id')
            )
            db.session.add(d)
            db.session.commit()
            # Audit log
            logger.info(f"user=API action=CREATE diamond_id={d.id} cert={d.certificate_no}")
            return diamond_to_dict(d), 201
        except IntegrityError:
            db.session.rollback()
            diamond_ns.abort(400, 'Database integrity error occurred.')
        except Exception as e:
            db.session.rollback()
            diamond_ns.abort(400, f'An error occurred: {str(e)}')

# Consolidated Diamond CRUD operations
@diamond_ns.route('/diamonds/<int:diamond_id>')
class DiamondCRUD(Resource):
    @diamond_ns.doc('get_diamond')
    @diamond_ns.marshal_with(diamond_model)
    @diamond_ns.response(200, 'Diamond details', diamond_model)
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @diamond_ns.response(404, 'Diamond not found', error_model)
    @token_required
    @handle_errors
    def get(self, diamond_id):
        """Get diamond by ID."""
        d = Diamond.query.get_or_404(diamond_id)
        return diamond_to_dict(d), 200

    @diamond_ns.doc('update_diamond')
    @diamond_ns.expect(diamond_update_model, validate=True)
    @diamond_ns.marshal_with(diamond_model)
    @diamond_ns.response(400, 'Validation error', error_model)
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    @handle_errors
    def put(self, diamond_id):
        d = Diamond.query.get_or_404(diamond_id)
        data = request.json
        try:
            errors = validate_diamond_fields(data, is_update=True)
            if errors:
                diamond_ns.abort(400, '; '.join(errors))
            for field in ['shape_id', 'size_mm', 'carat', 'clarity', 'color', 'certificate_no', 'quantity', 'status']:
                if field in data:
                    setattr(d, field, data[field])
            if 'purchase_date' in data:
                d.purchase_date = datetime.strptime(data['purchase_date'], '%Y-%m-%d').date()
            # Vendor validation
            if 'vendor_id' in data:
                vendor = Vendor.query.get(data['vendor_id'])
                if not vendor:
                    diamond_ns.abort(400, f"Vendor with id {data['vendor_id']} does not exist.")
                d.vendor_id = data['vendor_id']
            db.session.commit()
            # Audit log
            logger.info(f"user=API action=UPDATE diamond_id={d.id} cert={d.certificate_no}")
            return diamond_to_dict(d), 200
        except IntegrityError:
            db.session.rollback()
            diamond_ns.abort(400, 'Database integrity error occurred.')
        except ValueError as ve:
            db.session.rollback()
            diamond_ns.abort(400, f'Value error: {str(ve)}')
        except KeyError as ke:
            db.session.rollback()
            diamond_ns.abort(400, f'Key error: {str(ke)}')
        except Exception as e:
            db.session.rollback()
            diamond_ns.abort(400, f'An unexpected error occurred: {str(e)}')

    @diamond_ns.doc('delete_diamond')
    @diamond_ns.response(200, 'Diamond deleted')
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    @admin_required
    @handle_errors
    def delete(self, diamond_id):
        d = Diamond.query.get_or_404(diamond_id)
        try:
            db.session.delete(d)
            db.session.commit()
            # Audit log
            logger.info(f"user=API action=DELETE diamond_id={d.id} cert={d.certificate_no}")
            return {'message': 'Diamond deleted'}, 200
        except Exception as e:
            db.session.rollback()
            diamond_ns.abort(400, f'An error occurred: {str(e)}')

@diamond_ns.route('/diamonds/<int:diamond_id>/deduct')
@diamond_ns.response(404, 'Diamond not found', error_model)
class DiamondDeduct(Resource):
    @diamond_ns.doc('deduct_diamond_quantity')
    @diamond_ns.expect(diamond_ns.model('DeductQuantity', {
        'quantity': fields.Integer(required=True)
    }), validate=True)
    @diamond_ns.marshal_with(diamond_model)
    @diamond_ns.response(400, 'Not enough quantity', error_model)
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    @handle_errors
    def patch(self, diamond_id):
        d = Diamond.query.get_or_404(diamond_id)
        data = request.json
        qty = data.get('quantity')

        if not isinstance(qty, int) or qty <= 0:
            diamond_ns.abort(400, 'Quantity must be a positive integer.')

        if d.quantity < qty:
            diamond_ns.abort(400, f'Not enough stock. Available: {d.quantity}, Requested: {qty}')
            
        d.quantity -= qty
        if d.quantity == 0:
            d.status = 'used'
        db.session.commit()
        return diamond_to_dict(d), 200

# Shape list operations
@diamond_ns.route('/shapes')
class ShapeList(Resource):
    @diamond_ns.doc('list_shapes')
    @diamond_ns.marshal_list_with(shape_model)
    @diamond_ns.response(200, 'List of shapes', [shape_model])
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    @handle_errors
    def get(self):
        """Get all shapes."""
        shapes = Shape.query.all()
        return [{'id': s.id, 'name': s.name} for s in shapes]

    @diamond_ns.doc('create_shape')
    @diamond_ns.expect(shape_model, validate=True)
    @diamond_ns.marshal_with(shape_model, code=201)
    @diamond_ns.response(400, 'Validation error', error_model)
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    @handle_errors
    def post(self):
        """Create a new shape."""
        data = request.json
        # Normalize name: trim and capitalize first letter
        name = data['name'].strip().capitalize()
        # Check for existing shape (case-insensitive)
        existing = Shape.query.filter(db.func.lower(Shape.name) == name.lower()).first()
        if existing:
            diamond_ns.abort(409, 'Shape name already exists.')
        shape = Shape(name=name)
        db.session.add(shape)
        db.session.commit()
        return {'id': shape.id, 'name': shape.name}, 201

# Consolidated Shape Management
@diamond_ns.route('/shapes/<int:shape_id>')
class ShapeCRUD(Resource):
    @diamond_ns.doc('get_shape')
    @diamond_ns.marshal_with(shape_model)
    @diamond_ns.response(200, 'Shape details', shape_model)
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @diamond_ns.response(404, 'Shape not found', error_model)
    @token_required
    @handle_errors
    def get(self, shape_id):
        """Get shape by ID."""
        shape = Shape.query.get_or_404(shape_id)
        return {'id': shape.id, 'name': shape.name}, 200

    @diamond_ns.doc('delete_shape')
    @diamond_ns.response(204, 'Shape deleted')
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    @admin_required
    @handle_errors
    def delete(self, shape_id):
        shape = Shape.query.get_or_404(shape_id)
        db.session.delete(shape)
        db.session.commit()
        return '', 204

@diamond_ns.route('/manufacturing-types')
class ManufacturingTypesList(Resource):
    @diamond_ns.doc('list_manufacturing_types')
    def get(self):
        """List all manufacturing process types"""
        manufacturing = Manufacturing.query.all()
        return [{'id': m.id, 'name': m.name} for m in manufacturing]

    @diamond_ns.doc('create_manufacturing_type')
    def post(self):
        """Create a new manufacturing process type"""
        data = request.json
        m = Manufacturing(name=data['name'])
        db.session.add(m)
        db.session.commit()
        return {'id': m.id, 'name': m.name}, 201

@diamond_ns.route('/jewelry')
class JewelryList(Resource):
    @diamond_ns.doc('list_jewelry')
    def get(self):
        """List all jewelry items"""
        jewelry = Jewelry.query.all()
        return [{'id': j.id, 'name': j.name} for j in jewelry]

    @diamond_ns.doc('create_jewelry')
    def post(self):
        """Create a new jewelry item"""
        data = request.json
        j = Jewelry(name=data['name'])
        db.session.add(j)
        db.session.commit()
        return {'id': j.id, 'name': j.name}, 201

@diamond_ns.route('/diamonds/<int:diamond_id>/assign-manufacturing/<int:manufacturing_id>')
class AssignManufacturing(Resource):
    @diamond_ns.doc('assign_manufacturing')
    def patch(self, diamond_id, manufacturing_id):
        """Assign a diamond to a manufacturing process"""
        diamond = Diamond.query.get_or_404(diamond_id)
        manufacturing = Manufacturing.query.get_or_404(manufacturing_id)
        diamond.manufacturing_id = manufacturing.id
        db.session.commit()
        return {'message': 'Diamond assigned to manufacturing'}, 200

@diamond_ns.route('/diamonds/<int:diamond_id>/assign-jewelry/<int:jewelry_id>')
class AssignJewelry(Resource):
    @diamond_ns.doc('assign_jewelry')
    def patch(self, diamond_id, jewelry_id):
        """Assign a diamond to a jewelry item"""
        diamond = Diamond.query.get_or_404(diamond_id)
        jewelry = Jewelry.query.get_or_404(jewelry_id)
        diamond.jewelry_id = jewelry.id
        db.session.commit()
        return {'message': 'Diamond assigned to jewelry'}, 200
