{"data_mtime": 1753034638, "dep_lines": [4, 5, 1, 2, 3, 6, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["app.models.user", "app.utils.error_handler", "functools", "flask", "flask_jwt_extended", "app", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "0fe8fd983afa9724a9364973e81ad15d1c4b175a", "id": "app.utils.decorators", "ignore_all": true, "interface_hash": "56b9adbe063366ea1113cd5ae047d6a92f5a0a90", "mtime": 1752317250, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\admin_panel\\admin_backend\\app\\utils\\decorators.py", "plugin_data": null, "size": 2094, "suppressed": [], "version_id": "1.15.0"}