from flask_restx import Namespace, Resource, fields
from flask import request, render_template, make_response, current_app
from app.models.sale import Sale
from app.models.jewelry import JewelryItem
from app import db
from app.utils.decorators import token_required
from datetime import datetime
import io
from xhtml2pdf import pisa
from sqlalchemy.exc import IntegrityError

sale_ns = Namespace('sales', description='Sales and invoice management', path='/sales')

sale_model = sale_ns.model('Sale', {
    'id': fields.Integer(readOnly=True),
    'invoice_no': fields.String(required=True),
    'customer_name': fields.String(required=True),
    'sale_date': fields.String,
    'total_amount': fields.Float(required=True),
    'payment_status': fields.String,
    'jewelry_id': fields.Integer(required=True)
})

create_sale_model = sale_ns.model('CreateSale', {
    'invoice_no': fields.String(required=True),
    'customer_name': fields.String(required=True),
    'sale_date': fields.String,
    'total_amount': fields.Float(required=True),
    'payment_status': fields.String,
    'jewelry_id': fields.Integer(required=True)
})

error_model = sale_ns.model('SaleError', {
    'status': fields.String(description='Error status'),
    'message': fields.String(description='Error message'),
    'status_code': fields.Integer(description='HTTP status code')
})

def sale_to_dict(s):
    return {
        'id': s.id,
        'invoice_no': s.invoice_no,
        'customer_name': s.customer_name,
        'sale_date': s.sale_date.isoformat() if s.sale_date else None,
        'total_amount': s.total_amount,
        'payment_status': s.payment_status,
        'jewelry_id': s.jewelry_id,
        'created_at': s.created_at.isoformat() if s.created_at else None,
        'jewelry': {
            'id': s.jewelry_item.id,
            'name': s.jewelry_item.name,
            'design_code': s.jewelry_item.design_code,
            'gross_weight': s.jewelry_item.gross_weight,
            'metal_type': s.jewelry_item.metal_type,
            'vendor': {
                'name': s.jewelry_item.vendor.name
            } if s.jewelry_item.vendor else None,
            'diamonds': [
                {
                    'diamond_id': d.id,
                    'quantity': db.session.execute(jewelry_diamonds.select().where(jewelry_diamonds.c.diamond_id == d.id)).first().quantity,
                    'diamond': {
                        'shape': d.shape.name if d.shape else None,
                        'carat': d.carat,
                        'clarity': d.clarity,
                        'color': d.color,
                        'certificate_no': d.certificate_no,
                        'size_mm': d.size_mm,
                        'status': d.status
                    }
                } for d in s.jewelry_item.diamonds
            ]
        } if s.jewelry_item else None
    }

@sale_ns.route('/')
class SaleList(Resource):
    @sale_ns.doc('list_sales')
    @sale_ns.response(200, 'List of sales', [sale_model])
    @sale_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def get(self):
        """List all sales (optionally filter by customer_name/payment_status/date range)"""
        query = Sale.query
        for field in ['customer_name', 'payment_status']:
            value = request.args.get(field)
            if value:
                query = query.filter(getattr(Sale, field) == value)
        start = request.args.get('start_date')
        end = request.args.get('end_date')
        if start:
            query = query.filter(Sale.sale_date >= datetime.strptime(start, '%Y-%m-%d').date())
        if end:
            query = query.filter(Sale.sale_date <= datetime.strptime(end, '%Y-%m-%d').date())
        
        # Pagination
        page = request.args.get('page', default=1, type=int)
        limit = request.args.get('limit', default=20, type=int)
        pagination = query.paginate(page=page, per_page=limit, error_out=False)
        sales = pagination.items
        return {
            'data': [sale_to_dict(s) for s in sales],
            'total': pagination.total,
            'page': page,
            'limit': limit
        }, 200

    @sale_ns.doc('create_sale')
    @sale_ns.expect(create_sale_model)
    @sale_ns.marshal_with(sale_model, code=201)
    @sale_ns.response(400, 'Validation error', error_model)
    @sale_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def post(self):
        """Create a new sale"""
        data = request.get_json()
        try:
            jewelry = JewelryItem.query.get(data['jewelry_id'])
            if not jewelry or jewelry.status == 'sold':
                sale_ns.abort(400, 'Jewelry not available')
            s = Sale(
                invoice_no=data['invoice_no'],
                customer_name=data['customer_name'],
                sale_date=datetime.strptime(data['sale_date'], '%Y-%m-%d').date() if 'sale_date' in data else None,
                total_amount=data['total_amount'],
                payment_status=data.get('payment_status', 'unpaid'),
                jewelry_id=data['jewelry_id']
            )
            jewelry.status = 'sold'
            db.session.add(s)
            db.session.commit()
            return sale_to_dict(s), 201
        except IntegrityError:
            db.session.rollback()
            return {"message": "Duplicate invoice_no", "status": "error", "status_code": 409}, 409
        except Exception as e:
            db.session.rollback()
            sale_ns.abort(400, f'Failed to create sale: {str(e)}')

@sale_ns.route('/<int:sale_id>')
@sale_ns.param('sale_id', 'The sale identifier')
class SaleResource(Resource):
    @sale_ns.doc('get_sale')
    @sale_ns.marshal_with(sale_model)
    @token_required
    def get(self, sale_id):
        """Get a sale by ID"""
        s = Sale.query.get_or_404(sale_id)
        return sale_to_dict(s)

@sale_ns.route('/<int:sale_id>/mark-paid')
@sale_ns.param('sale_id', 'The sale identifier')
class SaleMarkPaid(Resource):
    @sale_ns.doc('mark_sale_paid')
    @sale_ns.marshal_with(sale_model)
    @token_required
    def patch(self, sale_id):
        """Mark a sale as paid"""
        s = Sale.query.get_or_404(sale_id)
        s.payment_status = 'paid'
        db.session.commit()
        return sale_to_dict(s)

@sale_ns.route('/invoice/<int:sale_id>')
@sale_ns.param('sale_id', 'The sale identifier')
class SaleInvoice(Resource):
    @sale_ns.doc('download_invoice')
    @token_required
    def get(self, sale_id):
        """Download invoice as PDF for a sale"""
        s = Sale.query.get_or_404(sale_id)
        jewelry = JewelryItem.query.get(s.jewelry_id)
        html = render_template('invoice_template.html', sale=s, jewelry=jewelry)
        try:
            pdf = io.BytesIO()
            result = pisa.CreatePDF(io.StringIO(html), dest=pdf)
            if result.err:
                current_app.logger.error(f"PDF generation failed: {result.err}")
                sale_ns.abort(500, "Failed to generate PDF")
            pdf.seek(0)
            response = make_response(pdf.read())
            response.headers['Content-Type'] = 'application/pdf'
            response.headers['Content-Disposition'] = f'attachment; filename=invoice_{s.invoice_no}.pdf'
            return response
        except (IOError, ValueError) as pdf_error:
            current_app.logger.error(f"PDF generation failed: {pdf_error}")
            sale_ns.abort(500, "Failed to generate PDF")
        except Exception as general_error:
            current_app.logger.error(f"Unexpected error during PDF generation: {str(general_error)}")
            sale_ns.abort(500, "An error occurred while generating the PDF")
