from functools import wraps
from flask import jsonify
from flask_jwt_extended import get_jwt_identity, verify_jwt_in_request
from app.models.user import User
from app.utils.error_handler import APIError
from app import db

def admin_required(fn):
    """Decorator to require admin role."""
    @wraps(fn)
    def wrapper(*args, **kwargs):
        try:
            current_user_id = get_jwt_identity()
            user = db.session.get(User, current_user_id)
            
            if not user:
                raise APIError('User not found', 404)
            
            if not user.is_admin():
                raise APIError('Admin access required', 403)
            
            return fn(*args, **kwargs)
        except APIError as e:
            return e.to_dict(), e.status_code
        except Exception as e:
            raise APIError('Authentication error', 401)
    return wrapper

def role_required(roles):
    """Decorator to require specific roles."""
    def decorator(fn):
        @wraps(fn)
        def wrapper(*args, **kwargs):
            try:
                current_user_id = get_jwt_identity()
                user = db.session.get(User, current_user_id)
                
                if not user:
                    raise APIError('User not found', 404)
                
                if user.role.name not in roles:
                    raise APIError('Insufficient permissions', 403)
                
                return fn(*args, **kwargs)
            except APIError as e:
                return e.to_dict(), e.status_code
            except Exception as e:
                raise APIError('Authentication error', 401)
        return wrapper
    return decorator

def token_required(fn):
    """Decorator to require a valid JWT token for access."""
    @wraps(fn)
    def wrapper(*args, **kwargs):
        try:
            verify_jwt_in_request()
            return fn(*args, **kwargs)
        except Exception as e:
            # More detailed error handling for JWT issues
            error_msg = str(e)
            if 'expired' in error_msg.lower():
                return {'status': 'error', 'message': 'Token has expired'}, 401
            elif 'invalid' in error_msg.lower():
                return {'status': 'error', 'message': 'Invalid token'}, 401
            elif 'missing' in error_msg.lower() or 'authorization' in error_msg.lower():
                return {'status': 'error', 'message': 'Authorization token required'}, 401
            else:
                return {'status': 'error', 'message': f'Authentication error: {error_msg}'}, 401
    return wrapper