{"data_mtime": 1753037352, "dep_lines": [3, 4, 6, 7, 120, 8, 119, 2, 5, 9, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 20, 5, 20, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["app.models.jewelry", "app.models.diamond", "app.utils.decorators", "app.utils.error_handler", "app.models.vendor", "werkzeug.utils", "sqlalchemy.exc", "flask", "app", "datetime", "os", "builtins", "_frozen_importlib", "abc", "app.models", "app.utils", "flask.app", "flask.globals", "flask.scaffold", "flask.wrappers", "flask_sqlalchemy", "flask_sqlalchemy.extension", "typing", "werkzeug", "werkzeug.sansio", "werkzeug.sansio.request", "werkzeug.wrappers", "werkzeug.wrappers.request"], "hash": "2e997d8b7b8763a2ed835f47801734b429ad743d", "id": "app.api.jewelry", "ignore_all": true, "interface_hash": "206e84612e14200e81eb72db32acc40d0961dc33", "mtime": 1752317250, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\admin_panel\\admin_backend\\app\\api\\jewelry.py", "plugin_data": null, "size": 11617, "suppressed": ["flask_restx"], "version_id": "1.15.0"}