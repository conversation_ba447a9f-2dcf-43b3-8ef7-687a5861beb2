from app import db
from datetime import date

# Association table for many-to-many diamond-manufacturing with quantity
manufacturing_diamonds = db.Table(
    'manufacturing_diamonds',
    db.Column('manufacturing_id', db.Integer, db.<PERSON>ey('manufacturing_requests.id', name='fk_manufacturing_diamonds_manufacturing_id'), primary_key=True),
    db.<PERSON>umn('diamond_id', db.Integer, db.<PERSON>('diamonds.id', name='fk_manufacturing_diamonds_diamond_id'), primary_key=True),
    db.Column('quantity', db.Integer, nullable=False, default=1)
)

class ManufacturingRequest(db.Model):
    __tablename__ = 'manufacturing_requests'
    id = db.Column(db.Integer, primary_key=True)
    vendor_id = db.Column(db.Integer, db.ForeignKey('vendors.id', name='fk_manufacturing_vendor_id'), nullable=False)
    sent_date = db.Column(db.Date, default=date.today)
    expected_return_date = db.Column(db.Date, nullable=True)
    return_date = db.Column(db.Date)  # Added return date
    status = db.Column(db.String(20), default='open')  # open, completed
    created_at = db.Column(db.DateTime, default=db.func.now())
    
    vendor = db.relationship('Vendor', backref='manufacturing_requests')
    diamonds = db.relationship('Diamond', secondary=manufacturing_diamonds, backref='manufacturing_requests')

    def __repr__(self):
        return f'<ManufacturingRequest {self.id} - Vendor {self.vendor_id}>'
