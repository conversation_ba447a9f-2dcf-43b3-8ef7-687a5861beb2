{"data_mtime": 1753034597, "dep_lines": [5, 36, 3, 3, 6, 7, 1, 2, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "dep_prios": [5, 20, 10, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["app.utils.error_handler", "app.models.role", "marshmallow.fields", "marshmallow.validate", "app.config", "sqlalchemy.orm", "app", "datetime", "marshmallow", "builtins", "_frozen_importlib", "abc", "app.utils", "flask_sqlalchemy", "flask_sqlalchemy.extension", "marshmallow.base", "marshmallow.schema", "sqlalchemy", "sqlalchemy.log", "sqlalchemy.orm.base", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.relationships", "sqlalchemy.sql", "sqlalchemy.sql.cache_key", "sqlalchemy.util", "sqlalchemy.util.langhelpers", "typing"], "hash": "ecb078992b29cdcc563cb8f989799362b2931cda", "id": "app.models.user", "ignore_all": true, "interface_hash": "8323d51fd5a0f8ea87d162b7367d69e4250dc808", "mtime": 1750704331, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\admin_panel\\admin_backend\\app\\models\\user.py", "plugin_data": null, "size": 4387, "suppressed": ["passlib.hash"], "version_id": "1.15.0"}