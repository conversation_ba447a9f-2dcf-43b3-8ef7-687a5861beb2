{"data_mtime": 1753075997, "dep_lines": [4, 5, 7, 9, 10, 3, 6, 8, 11, 12, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["app.models.diamond", "app.models.vendor", "app.utils.decorators", "sqlalchemy.exc", "sqlalchemy.orm", "flask", "app", "datetime", "functools", "re", "logging", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "app.models", "app.utils", "flask.globals", "flask.wrappers", "flask_sqlalchemy", "flask_sqlalchemy.extension", "os", "sqlalchemy", "types", "typing", "werkzeug", "werkzeug.sansio", "werkzeug.sansio.request", "werkzeug.wrappers", "werkzeug.wrappers.request"], "hash": "f8dad5f23d1d84f3fa0e699a64042fa38f756d24", "id": "app.api.diamond", "ignore_all": true, "interface_hash": "58a504f88089a6ddd72b65588dc6e96536e41b69", "mtime": 1753075971, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\admin_panel\\admin_backend\\app\\api\\diamond.py", "plugin_data": null, "size": 28832, "suppressed": ["flask_restx"], "version_id": "1.15.0"}