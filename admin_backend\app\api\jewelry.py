from flask_restx import Namespace, Resource, fields, reqparse
from flask import request, current_app
from app.models.jewelry import JewelryItem, jewelry_diamonds
from app.models.diamond import Diamond
from app import db
from app.utils.decorators import token_required
from app.utils.error_handler import handle_errors
from werkzeug.utils import secure_filename
from datetime import datetime
import os

jewelry_ns = Namespace('jewelry', description='Jewelry inventory and management', path='/jewelry')

diamond_link_model = jewelry_ns.model('JewelryDiamond', {
    'diamond_id': fields.Integer(required=True),
    'quantity': fields.Integer(required=True)
})

jewelry_model = jewelry_ns.model('Jewelry', {
    'id': fields.Integer(readOnly=True),
    'name': fields.String(required=True),
    'design_code': fields.String(required=True),
    'vendor_id': fields.Integer(required=True),
    'gross_weight': fields.Float(required=True),
    'metal_type': fields.String(required=True),
    'received_date': fields.String,
    'status': fields.String,
    'image_path': fields.String,
    'diamonds': fields.List(fields.Nested(diamond_link_model))
})

create_jewelry_model = jewelry_ns.model('CreateJewelry', {
    'name': fields.String(required=True),
    'design_code': fields.String(required=True),
    'vendor_id': fields.Integer(required=True),
    'gross_weight': fields.Float(required=True),
    'metal_type': fields.String(required=True),
    'received_date': fields.String,
    'status': fields.String,
    'diamonds': fields.List(fields.Nested(diamond_link_model))
})

error_model = jewelry_ns.model('JewelryError', {
    'status': fields.String(description='Error status'),
    'message': fields.String(description='Error message'),
    'status_code': fields.Integer(description='HTTP status code')
})

def jewelry_to_dict(i):
    return {
        'id': i.id,
        'name': i.name,
        'design_code': i.design_code,
        'vendor_id': i.vendor_id,
        'vendor': {
            'id': i.vendor.id,
            'name': i.vendor.name
        } if i.vendor else None,
        'gross_weight': i.gross_weight,
        'metal_type': i.metal_type,
        'received_date': i.received_date.isoformat() if i.received_date else None,
        'status': i.status,
        'image_path': i.image_path,
        'created_at': i.created_at.isoformat() if i.created_at else None,
        'diamonds': [
            {
                'diamond_id': d.id,
                'quantity': db.session.execute(jewelry_diamonds.select().where(jewelry_diamonds.c.diamond_id == d.id)).first().quantity,
                'diamond': {
                    'shape': d.shape.name if d.shape else None,
                    'carat': d.carat,
                    'clarity': d.clarity,
                    'color': d.color,
                    'certificate_no': d.certificate_no,
                    'size_mm': d.size_mm,
                    'status': d.status
                }
            } for d in i.diamonds
        ]
    }

@jewelry_ns.route('/')
class JewelryList(Resource):
    @jewelry_ns.doc('list_jewelry')
    @jewelry_ns.response(200, 'List of jewelry items', [jewelry_model])
    @jewelry_ns.response(401, 'Unauthorized', error_model)
    @token_required
    @handle_errors
    def get(self):
        """List all jewelry items (optionally filter by status/vendor_id/design_code)"""
        query = JewelryItem.query
        for field in ['status', 'vendor_id', 'design_code']:
            value = request.args.get(field)
            if value:
                query = query.filter(getattr(JewelryItem, field) == value)
        
        # Pagination
        page = request.args.get('page', default=1, type=int)
        limit = request.args.get('limit', default=20, type=int)
        pagination = query.paginate(page=page, per_page=limit, error_out=False)
        items = pagination.items
        return {
            'data': [jewelry_to_dict(i) for i in items],
            'total': pagination.total,
            'page': page,
            'limit': limit
        }, 200

    @jewelry_ns.doc('create_jewelry')
    @jewelry_ns.expect(create_jewelry_model)
    @jewelry_ns.marshal_with(jewelry_model, code=201)
    @jewelry_ns.response(400, 'Validation error', error_model)
    @jewelry_ns.response(401, 'Unauthorized', error_model)
    @token_required
    @handle_errors
    def post(self):
        """Create a new jewelry item"""
        data = request.get_json()
        from sqlalchemy.exc import IntegrityError
        from app.models.vendor import Vendor
        try:
            # Unique name validation
            if JewelryItem.query.filter_by(name=data['name']).first():
                jewelry_ns.abort(409, f"Jewelry with name '{data['name']}' already exists.")
            # Vendor validation
            vendor = Vendor.query.get(data['vendor_id'])
            if not vendor:
                jewelry_ns.abort(400, f"Vendor with id {data['vendor_id']} does not exist.")
            # Quantity validation for diamonds
            for d in data.get('diamonds', []):
                if d['quantity'] <= 0:
                    jewelry_ns.abort(400, f"Diamond quantity must be greater than 0.")
            i = JewelryItem(
                name=data['name'],
                design_code=data['design_code'],
                vendor_id=data['vendor_id'],
                gross_weight=data['gross_weight'],
                metal_type=data['metal_type'],
                received_date=datetime.strptime(data['received_date'], '%Y-%m-%d').date() if 'received_date' in data else None,
                status=data.get('status', 'in_stock')
            )
            db.session.add(i)
            db.session.flush()  # Get i.id
            for d in data.get('diamonds', []):
                diamond = Diamond.query.get(d['diamond_id'])
                if not diamond:
                    db.session.rollback()
                    jewelry_ns.abort(404, f'Diamond {d["diamond_id"]} not found')
                db.session.execute(jewelry_diamonds.insert().values(
                    jewelry_id=i.id, diamond_id=diamond.id, quantity=d['quantity']
                ))
            db.session.commit()
            return jewelry_to_dict(i), 201
        except IntegrityError:
            db.session.rollback()
            jewelry_ns.abort(409, "Database integrity error occurred.")
        except Exception as e:
            db.session.rollback()
            jewelry_ns.abort(400, f'Failed to create jewelry item: {str(e)}')

@jewelry_ns.route('/<int:jewelry_id>')
@jewelry_ns.param('jewelry_id', 'The jewelry item identifier')
class JewelryResource(Resource):
    @jewelry_ns.doc('get_jewelry')
    @jewelry_ns.marshal_with(jewelry_model)
    @token_required
    @handle_errors
    def get(self, jewelry_id):
        """Get a jewelry item by ID"""
        i = JewelryItem.query.get_or_404(jewelry_id)
        return jewelry_to_dict(i)

    @jewelry_ns.doc('update_jewelry')
    @jewelry_ns.expect(create_jewelry_model)
    @jewelry_ns.marshal_with(jewelry_model)
    @token_required
    @handle_errors
    def put(self, jewelry_id):
        """Update a jewelry item"""
        i = JewelryItem.query.get_or_404(jewelry_id)
        data = request.get_json()
        for field in ['name', 'design_code', 'vendor_id', 'gross_weight', 'metal_type', 'status']:
            if field in data:
                setattr(i, field, data[field])
        if 'received_date' in data:
            i.received_date = datetime.strptime(data['received_date'], '%Y-%m-%d').date()
        db.session.commit()
        return jewelry_to_dict(i)

    @jewelry_ns.doc('delete_jewelry')
    @token_required
    @handle_errors
    def delete(self, jewelry_id):
        """Delete a jewelry item"""
        i = JewelryItem.query.get_or_404(jewelry_id)
        # Check for associated diamonds
        associated_diamonds = db.session.execute(
            jewelry_diamonds.select().where(jewelry_diamonds.c.jewelry_id == jewelry_id)
        ).fetchall()
        if associated_diamonds:
            jewelry_ns.abort(400, 'Cannot delete jewelry item with associated diamonds.')

        try:
            db.session.delete(i)
            db.session.commit()
            return {'message': 'Jewelry item deleted'}, 200
        except Exception as e:
            db.session.rollback()
            jewelry_ns.abort(400, f'Failed to delete jewelry item: {str(e)}')

# File upload parser for Swagger
upload_parser = reqparse.RequestParser()
upload_parser.add_argument('image', location='files', type='FileStorage', required=True)

@jewelry_ns.route('/<int:jewelry_id>/image')
@jewelry_ns.param('jewelry_id', 'The jewelry item identifier')
class JewelryImageUpload(Resource):
    @jewelry_ns.doc('upload_jewelry_image')
    @jewelry_ns.expect(upload_parser)
    @token_required
    @handle_errors
    def post(self, jewelry_id):
        """Upload an image for a jewelry item"""
        i = JewelryItem.query.get_or_404(jewelry_id)
        if 'image' not in request.files:
            jewelry_ns.abort(400, 'No image uploaded')
        image = request.files['image']
        filename = secure_filename(image.filename)
        upload_folder = os.path.join(current_app.root_path, 'static', 'uploads')
        os.makedirs(upload_folder, exist_ok=True)
        path = os.path.join(upload_folder, filename)
        image.save(path)
        i.image_path = f'/static/uploads/{filename}'
        db.session.commit()
        return {'image_path': i.image_path}

@jewelry_ns.route('/<int:jewelry_id>/mark-sold')
@jewelry_ns.param('jewelry_id', 'The jewelry item identifier')
class JewelryMarkSold(Resource):
    @jewelry_ns.doc('mark_jewelry_sold')
    @jewelry_ns.marshal_with(jewelry_model)
    @token_required
    @handle_errors
    def patch(self, jewelry_id):
        """Mark a jewelry item as sold"""
        i = JewelryItem.query.get_or_404(jewelry_id)
        i.status = 'sold'
        db.session.commit()
        return jewelry_to_dict(i)

@jewelry_ns.route('/<int:jewelry_id>/deduct-diamonds')
@jewelry_ns.param('jewelry_id', 'The jewelry item identifier')
class JewelryDeductDiamonds(Resource):
    @jewelry_ns.doc('deduct_diamonds_for_jewelry')
    @token_required
    @handle_errors
    def patch(self, jewelry_id):
        """Deduct diamonds used in a jewelry item"""
        i = JewelryItem.query.get_or_404(jewelry_id)
        associated_diamonds = db.session.execute(
            jewelry_diamonds.select().where(jewelry_diamonds.c.jewelry_id == jewelry_id)
        ).fetchall()

        if not associated_diamonds:
            jewelry_ns.abort(400, 'No associated diamonds to deduct.')

        try:
            for diamond in associated_diamonds:
                d = Diamond.query.get(diamond.diamond_id)
                if not d:
                    jewelry_ns.abort(400, f'Diamond with ID {diamond.diamond_id} does not exist.')
                if d.quantity < diamond.quantity:
                    jewelry_ns.abort(400, f'Not enough stock for diamond {diamond.diamond_id}')
                d.quantity -= diamond.quantity
                db.session.add(d)
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            jewelry_ns.abort(500, f'Failed to deduct diamonds: {str(e)}')

        return {'message': 'Diamonds deducted successfully'}, 200
