export interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
}

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface Diamond {
  id: number;
  shape: string;
  shape_id: number;

  // Basic Properties
  carat: number;

  // 4Cs - Industry Standard
  color: string; // D, E, F, G, H, I, J, K, L, M, N, O, P, Q, R, S, T, U, V, W, X, Y, Z
  clarity: string; // FL, IF, VVS1, VVS2, VS1, VS2, SI1, SI2, SI3, I1, I2, I3
  cut_grade?: string; // Excellent, Very Good, Good, Fair, Poor

  // Additional Grading
  polish?: string;
  symmetry?: string;
  fluorescence?: string;
  fluorescence_color?: string;

  // Measurements
  length_mm?: number;
  width_mm?: number;
  depth_mm?: number;
  depth_percent?: number;
  table_percent?: number;
  girdle?: string;
  culet?: string;

  // Certification
  certificate_no: string;
  certification_lab?: string;
  certificate_date?: string;
  certificate_url?: string;

  // Pricing
  cost_price?: number;
  retail_price?: number;
  market_value?: number;
  last_valuation_date?: string;

  // Inventory
  quantity: number;
  reserved_quantity?: number;
  available_quantity?: number;
  minimum_stock?: number;

  // Status and Location
  status: 'in_stock' | 'reserved' | 'sold' | 'manufacturing' | 'damaged' | 'lost';
  location?: string;
  notes?: string;

  // Relationships
  vendor_id?: number;
  vendorName?: string;

  // Dates
  purchase_date: string;
  created_at?: string;
  updated_at?: string;

  // Calculated Fields
  profit_margin?: number;
  profit_amount?: number;
  is_low_stock?: boolean;

  // Legacy
  size_mm?: string; // Deprecated, use length_mm, width_mm, depth_mm instead
}

export interface Vendor {
  id: number;
  name: string;
  gst_number: string;
  contact_number: string;
  address: string;
}

export interface ManufacturingRequest {
  id: number;
  vendor_id: number;
  vendor?: Vendor;
  sent_date: string;
  expected_return_date: string;
  return_date?: string;
  status: 'open' | 'completed';
  diamonds: Array<{
    diamond_id: number;
    diamond?: Diamond;
    quantity: number;
  }>;
  created_at?: string;
}

export interface Jewelry {
  id: number;
  name: string;
  design_code: string;
  vendor_id: number;
  vendor?: Vendor;
  gross_weight: number;
  metal_type: string;
  received_date: string;
  status: 'in_stock' | 'sold';
  image_path?: string | null;
  created_at?: string;
  diamonds?: Array<{
    diamond_id: number;
    quantity: number;
    diamond?: Diamond;
  }>;
}

export interface Sale {
  id: number;
  invoice_no: string;
  customer_name: string;
  sale_date: string;
  total_amount: number;
  payment_status: 'paid' | 'unpaid';
  jewelry_id: number;
  created_at?: string;
  jewelry?: Jewelry;
}

export interface DashboardActivity {
  type: string;
  description: string;
  date: string;
}

export interface DashboardSummary {
  diamonds_in_stock: number;
  jewelry_in_stock: number;
  jewelry_sold: number;
  total_sales: number;
  open_manufacturing: number;
  completed_manufacturing: number;
  total_inventory_value: number;
}

export interface SalesStats {
  total_sales: number;
  paid_sales: number;
  unpaid_sales: number;
}

export interface StockLevels {
  diamonds_in_stock: number;
  jewelry_in_stock: number;
}