export interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
}

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface Diamond {
  id: number;
  shape: string;
  shape_id: number;

  // Basic Properties
  carat: number;

  // 4Cs - Industry Standard
  color: string; // D, E, F, G, H, I, J, K, L, M, N, O, P, Q, R, S, T, U, V, W, X, Y, Z
  clarity: string; // FL, IF, VVS1, VVS2, VS1, VS2, SI1, SI2, SI3, I1, I2, I3
  cut_grade?: string; // Excellent, Very Good, Good, Fair, Poor

  // Additional Grading
  polish?: string;
  symmetry?: string;
  fluorescence?: string;
  fluorescence_color?: string;

  // Measurements
  length_mm?: number;
  width_mm?: number;
  depth_mm?: number;
  depth_percent?: number;
  table_percent?: number;
  girdle?: string;
  culet?: string;

  // Certification
  certificate_no: string;
  certification_lab?: string;
  certificate_date?: string;
  certificate_url?: string;

  // Pricing
  cost_price?: number;
  retail_price?: number;
  market_value?: number;
  last_valuation_date?: string;

  // Inventory
  quantity: number;
  reserved_quantity?: number;
  available_quantity?: number;
  minimum_stock?: number;

  // Status and Location
  status: 'in_stock' | 'reserved' | 'sold' | 'manufacturing' | 'damaged' | 'lost';
  location?: string;
  notes?: string;

  // Relationships
  vendor_id?: number;
  vendorName?: string;

  // Dates
  purchase_date: string;
  created_at?: string;
  updated_at?: string;

  // Calculated Fields
  profit_margin?: number;
  profit_amount?: number;
  is_low_stock?: boolean;

  // Legacy
  size_mm?: string; // Deprecated, use length_mm, width_mm, depth_mm instead
}

export interface Vendor {
  id: number;
  name: string;
  company_name?: string;
  vendor_code?: string;

  // Contact Information
  contact_person?: string;
  contact_number: string;
  alternate_contact?: string;
  email?: string;
  website?: string;

  // Address Information
  address: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;

  // Business Information
  gst_number: string;
  pan_number?: string;
  business_type?: string;
  specialization?: string;

  // Financial Information
  credit_limit?: number;
  payment_terms?: string;
  bank_name?: string;
  bank_account?: string;
  ifsc_code?: string;

  // Performance Metrics
  rating?: number;
  total_orders?: number;
  total_value?: number;

  // Status and Verification
  status?: 'active' | 'inactive' | 'blacklisted' | 'pending_verification';
  is_verified?: boolean;
  verification_date?: string;

  // Additional Information
  notes?: string;
  documents_path?: string;

  // Timestamps
  created_at?: string;
  updated_at?: string;
  last_order_date?: string;

  // Calculated Fields
  outstanding_amount?: number;
}

export interface ManufacturingRequest {
  id: number;
  order_number?: string;
  vendor_id: number;
  vendor?: Vendor;

  // Order Details
  order_type?: string;
  priority?: 'urgent' | 'high' | 'normal' | 'low';
  description?: string;
  special_instructions?: string;

  // Dates and Timeline
  sent_date: string;
  expected_return_date: string;
  actual_return_date?: string;
  promised_delivery_date?: string;

  // Status Tracking
  status: 'draft' | 'sent' | 'in_progress' | 'quality_check' | 'completed' | 'cancelled' | 'returned' | 'on_hold';
  progress_percentage?: number;

  // Quality and Inspection
  quality_check_status?: 'passed' | 'failed' | 'pending' | 'needs_rework';
  quality_notes?: string;
  inspector_name?: string;
  inspection_date?: string;

  // Financial Information
  estimated_cost?: number;
  actual_cost?: number;
  advance_paid?: number;
  balance_amount?: number;
  payment_status?: 'pending' | 'partial' | 'paid' | 'overdue';

  // Weight Tracking
  total_original_weight?: number;
  total_final_weight?: number;
  total_loss_weight?: number;
  loss_percentage?: number;

  // Additional Information
  notes?: string;
  internal_notes?: string;
  images_path?: string;

  // Tracking and Audit
  created_by?: string;
  updated_by?: string;
  created_at?: string;
  updated_at?: string;

  // Relationships
  diamonds: Array<{
    diamond_id: number;
    diamond?: Diamond;
    quantity: number;
    original_weight?: number;
    final_weight?: number;
    loss_weight?: number;
    notes?: string;
  }>;

  // Calculated Fields
  is_overdue?: boolean;
  days_remaining?: number;

  // Legacy fields for backward compatibility
  return_date?: string; // Use actual_return_date instead
}

export interface Jewelry {
  id: number;
  name: string;
  design_code: string;
  vendor_id: number;
  vendor?: Vendor;
  gross_weight: number;
  metal_type: string;
  received_date: string;
  status: 'in_stock' | 'sold';
  image_path?: string | null;
  created_at?: string;
  diamonds?: Array<{
    diamond_id: number;
    quantity: number;
    diamond?: Diamond;
  }>;
}

export interface Sale {
  id: number;
  invoice_no: string;
  customer_name: string;
  sale_date: string;
  total_amount: number;
  payment_status: 'paid' | 'unpaid';
  jewelry_id: number;
  created_at?: string;
  jewelry?: Jewelry;
}

export interface DashboardActivity {
  type: string;
  description: string;
  date: string;
}

export interface DashboardSummary {
  diamonds_in_stock: number;
  jewelry_in_stock: number;
  jewelry_sold: number;
  total_sales: number;
  open_manufacturing: number;
  completed_manufacturing: number;
  total_inventory_value: number;
}

export interface SalesStats {
  total_sales: number;
  paid_sales: number;
  unpaid_sales: number;
}

export interface StockLevels {
  diamonds_in_stock: number;
  jewelry_in_stock: number;
}