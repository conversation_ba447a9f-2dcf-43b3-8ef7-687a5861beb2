export interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
}

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface Diamond {
  id: number;
  shape: string;
  shape_id: number;
  size_mm: string; // Updated to String to handle both formats
  carat: number;
  clarity: string;
  color: string;
  certificate_no: string;
  quantity: number;
  purchase_date: string;
  status: 'in_stock' | 'reserved' | 'used';
  vendorName: string;
  vendor_id?: number;
}

export interface Vendor {
  id: number;
  name: string;
  gst_number: string;
  contact_number: string;
  address: string;
}

export interface ManufacturingRequest {
  id: number;
  vendor_id: number;
  vendor?: Vendor;
  sent_date: string;
  expected_return_date: string;
  return_date?: string;
  status: 'open' | 'completed';
  diamonds: Array<{
    diamond_id: number;
    diamond?: Diamond;
    quantity: number;
  }>;
  created_at?: string;
}

export interface Jewelry {
  id: number;
  name: string;
  design_code: string;
  vendor_id: number;
  vendor?: Vendor;
  gross_weight: number;
  metal_type: string;
  received_date: string;
  status: 'in_stock' | 'sold';
  image_path?: string | null;
  created_at?: string;
  diamonds?: Array<{
    diamond_id: number;
    quantity: number;
    diamond?: Diamond;
  }>;
}

export interface Sale {
  id: number;
  invoice_no: string;
  customer_name: string;
  sale_date: string;
  total_amount: number;
  payment_status: 'paid' | 'unpaid';
  jewelry_id: number;
  created_at?: string;
  jewelry?: Jewelry;
}

export interface DashboardActivity {
  type: string;
  description: string;
  date: string;
}

export interface DashboardSummary {
  diamonds_in_stock: number;
  jewelry_in_stock: number;
  jewelry_sold: number;
  total_sales: number;
  open_manufacturing: number;
  completed_manufacturing: number;
  total_inventory_value: number;
}

export interface SalesStats {
  total_sales: number;
  paid_sales: number;
  unpaid_sales: number;
}

export interface StockLevels {
  diamonds_in_stock: number;
  jewelry_in_stock: number;
}