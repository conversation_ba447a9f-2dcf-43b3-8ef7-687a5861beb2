import React from 'react';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery } from '@tanstack/react-query';
import { api } from '../../lib/api';
import { Diamond } from '../../types';
import Input from '../../components/ui/Input';
import Select from '../../components/ui/Select';
import Button from '../../components/ui/Button';
import Modal from '../../components/ui/Modal';
import toast from 'react-hot-toast';
import {
  DIAMOND_COLORS,
  DIAMOND_CLARITIES,
  CUT_GRADES,
  POLISH_SYMMETRY_GRADES,
  FLUORESCENCE_INTENSITIES,
  FLUORESCENCE_COLORS,
  DIAMOND_STATUSES,
  CERTIFICATION_LABS,
  CULET_SIZES,
  GIRDLE_DESCRIPTIONS,
  VALIDATION_RANGES
} from '../../constants/diamond';

interface DiamondFormProps {
  diamond?: Diamond;
  onSuccess: () => void;
}

interface DiamondFormData {
  // Basic Properties
  shape_id: string;
  carat: number;

  // 4Cs - Industry Standard
  color: string;
  clarity: string;
  cut_grade?: string;

  // Additional Grading
  polish?: string;
  symmetry?: string;
  fluorescence?: string;
  fluorescence_color?: string;

  // Measurements
  length_mm?: number;
  width_mm?: number;
  depth_mm?: number;
  depth_percent?: number;
  table_percent?: number;
  girdle?: string;
  culet?: string;

  // Certification
  certificate_no: string;
  certification_lab?: string;
  certificate_date?: string;
  certificate_url?: string;

  // Pricing
  cost_price?: number;
  retail_price?: number;
  market_value?: number;
  last_valuation_date?: string;

  // Inventory
  quantity: number;
  reserved_quantity?: number;
  minimum_stock?: number;

  // Status and Location
  status: 'in_stock' | 'reserved' | 'sold' | 'manufacturing' | 'damaged' | 'lost';
  location?: string;
  notes?: string;

  // Relationships
  vendor_id?: string;

  // Dates
  purchase_date: string;

  // Legacy
  size_mm?: string;
}

const DiamondForm: React.FC<DiamondFormProps> = ({ diamond, onSuccess }) => {
  const [showAddShapeModal, setShowAddShapeModal] = React.useState(false);
  const [newShapeName, setNewShapeName] = React.useState('');
  const [shapeOptionsState, setShapeOptionsState] = React.useState<{ value: string; label: string }[]>([]);
  const isEditing = !!diamond;
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset
  } = useForm<DiamondFormData>({
    defaultValues: diamond ? {
      shape_id: diamond.shape_id?.toString() || '',
      carat: diamond.carat,
      color: diamond.color,
      clarity: diamond.clarity,
      cut_grade: diamond.cut_grade || '',
      polish: diamond.polish || '',
      symmetry: diamond.symmetry || '',
      fluorescence: diamond.fluorescence || '',
      fluorescence_color: diamond.fluorescence_color || '',
      length_mm: diamond.length_mm,
      width_mm: diamond.width_mm,
      depth_mm: diamond.depth_mm,
      depth_percent: diamond.depth_percent,
      table_percent: diamond.table_percent,
      girdle: diamond.girdle || '',
      culet: diamond.culet || '',
      certificate_no: diamond.certificate_no,
      certification_lab: diamond.certification_lab || '',
      certificate_date: diamond.certificate_date
        ? (diamond.certificate_date.includes('T') ? diamond.certificate_date.split('T')[0] : diamond.certificate_date)
        : '',
      certificate_url: diamond.certificate_url || '',
      cost_price: diamond.cost_price,
      retail_price: diamond.retail_price,
      market_value: diamond.market_value,
      last_valuation_date: diamond.last_valuation_date
        ? (diamond.last_valuation_date.includes('T') ? diamond.last_valuation_date.split('T')[0] : diamond.last_valuation_date)
        : '',
      quantity: diamond.quantity,
      reserved_quantity: diamond.reserved_quantity,
      minimum_stock: diamond.minimum_stock,
      status: diamond.status,
      location: diamond.location || '',
      notes: diamond.notes || '',
      vendor_id: diamond.vendor_id?.toString() || '',
      purchase_date: diamond.purchase_date
        ? (diamond.purchase_date.includes('T') ? diamond.purchase_date.split('T')[0] : diamond.purchase_date)
        : '',
      size_mm: diamond.size_mm || ''
    } : {
      quantity: 1,
      minimum_stock: 1,
      status: 'in_stock' as const
    }
  });

  // Ensure form fields update when editing a different diamond
  React.useEffect(() => {
    if (diamond) {
      reset({
        shape_id: diamond.shape_id?.toString() || '',
        size_mm: diamond.size_mm.includes('*') ? diamond.size_mm : diamond.size_mm,
        carat: diamond.carat,
        clarity: diamond.clarity,
        color: diamond.color,
        certificate_no: diamond.certificate_no,
        quantity: diamond.quantity,
        purchase_date: diamond.purchase_date
          ? (diamond.purchase_date.includes('T') ? diamond.purchase_date.split('T')[0] : diamond.purchase_date)
          : '',
        status: diamond.status,
        vendor_id: diamond.vendor_id?.toString() || ''
      });
    }
  }, [diamond, reset]);
  const mutation = useMutation({
    mutationFn: async (data: DiamondFormData) => {
      // Convert shape_id from string to number
      const payload = {
        ...data,
        shape_id: parseInt(data.shape_id)
      };
      
      if (isEditing) {
        await api.diamonds.update(diamond.id, payload);
      } else {
        await api.diamonds.create(payload);
      }
    },
    onSuccess: () => {
      toast.success(`Diamond ${isEditing ? 'updated' : 'created'} successfully`);
      onSuccess();
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || `Failed to ${isEditing ? 'update' : 'create'} diamond`;
      toast.error(message);
    }
  });

  const onSubmit = (data: DiamondFormData) => {
    if (!validateSizeInput(data.size_mm)) {
      toast.error(getSizeErrorMessage());
      return;
    }

    let vendor_id: number | undefined = undefined;
    if (data.vendor_id !== undefined && data.vendor_id !== '' && !isNaN(Number(data.vendor_id))) {
      vendor_id = Number(data.vendor_id);
    }
    const payload = {
      ...data,
      vendor_id
    };
    mutation.mutate(payload as DiamondFormData);
  };

  // Fetch shapes and keep in local state for immediate refresh after add
  const { data: shapeOptionsRaw = [], isLoading: isLoadingShapes, isError: isErrorShapes, refetch: refetchShapes } = useQuery({
    queryKey: ['shapes'],
    queryFn: async () => {
      const response = await api.shapes.list();
      // Support both array and {data: array} for compatibility
      const arr = Array.isArray(response) ? response : response?.data;
      if (!Array.isArray(arr)) throw new Error('Failed to fetch shapes');
      // Sort alphabetically for UX
      return arr
        .map((shape: { id: number; name: string }) => ({
          value: shape.id.toString(),
          label: shape.name
        }))
        .sort((a, b) => a.label.localeCompare(b.label));
    },
  });
  React.useEffect(() => {
    setShapeOptionsState(Array.isArray(shapeOptionsRaw) ? shapeOptionsRaw : []);
  }, [shapeOptionsRaw]);

  const { data: vendorOptions, isLoading: isLoadingVendors } = useQuery({
    queryKey: ['vendors'],
    queryFn: async () => {
      const response = await api.vendors.list();
      const arr = Array.isArray(response) ? response : response?.data;
      if (!Array.isArray(arr)) throw new Error('Failed to fetch vendors');
      return arr.map((vendor: { id: number; name: string }) => ({
        value: vendor.id.toString(),
        label: vendor.name
      }));
    },
  });

  const clarityOptions = [
    { value: 'FL', label: 'FL (Flawless)' },
    { value: 'IF', label: 'IF (Internally Flawless)' },
    { value: 'VVS1', label: 'VVS1' },
    { value: 'VVS2', label: 'VVS2' },
    { value: 'VS1', label: 'VS1' },
    { value: 'VS2', label: 'VS2' },
    { value: 'SI1', label: 'SI1' },
    { value: 'SI2', label: 'SI2' },
    { value: 'I1', label: 'I1' },
    { value: 'I2', label: 'I2' },
    { value: 'I3', label: 'I3' }
  ];

  const colorOptions = [
    { value: 'D', label: 'D (Colorless)' },
    { value: 'E', label: 'E (Colorless)' },
    { value: 'F', label: 'F (Colorless)' },
    { value: 'G', label: 'G (Near Colorless)' },
    { value: 'H', label: 'H (Near Colorless)' },
    { value: 'I', label: 'I (Near Colorless)' },
    { value: 'J', label: 'J (Near Colorless)' },
    { value: 'K', label: 'K (Faint Yellow)' },
    { value: 'L', label: 'L (Faint Yellow)' },
    { value: 'M', label: 'M (Faint Yellow)' }
  ];

  const statusOptions = [
    { value: 'in_stock', label: 'In Stock' },
    { value: 'reserved', label: 'Reserved' },
    { value: 'used', label: 'Used' }
  ];

  // Add shape handler
  const [addShapeError, setAddShapeError] = React.useState<string | null>(null);
  const inputRef = React.useRef<HTMLInputElement>(null);
  const addShapeMutation = useMutation({
    mutationFn: async (name: string) => {
      setAddShapeError(null);
      return await api.shapes.create({ name });
    },
    onSuccess: async (newShape) => {
      toast.success('Shape added successfully');
      setShowAddShapeModal(false);
      setNewShapeName('');
      await refetchShapes();
      // Auto-select the new shape
      if (newShape && newShape.id) {
        // Use requestAnimationFrame for better UI responsiveness
        requestAnimationFrame(() => {
          setValue('shape_id', newShape.id.toString());
        });
      }
      // Use requestAnimationFrame for focusing input
      requestAnimationFrame(() => inputRef.current?.focus());
    },
    onError: (error: any) => {
      let message = error?.response?.data?.message || 'Failed to add shape';
      if (error?.response?.status === 409) message = 'Shape name already exists.';
      setAddShapeError(message);
      toast.error(message);
    }
  });
  React.useEffect(() => {
    if (showAddShapeModal) setTimeout(() => inputRef.current?.focus(), 100);
  }, [showAddShapeModal]);

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">        
        <div className="flex flex-col space-y-1">
          <div className="flex items-center justify-between">
            <label className="font-medium">Shape</label>
            <Button type="button" variant="secondary" size="sm" onClick={() => setShowAddShapeModal(true)}>
              + Add Shape
            </Button>
          </div>
          <Select
            options={isLoadingShapes ? [] : shapeOptionsState}
            required
            disabled={isLoadingShapes || isErrorShapes}
            {...register('shape_id', { required: 'Shape is required' })}
            error={errors.shape_id?.message || (isErrorShapes ? 'Failed to load shapes' : undefined)}
          />
        </div>

        <Input
          label="Size (mm)"
          type="text"
          placeholder="e.g., 6.50 or 5.50*7.90"
          required
          {...register('size_mm', {
            required: 'Size is required',
            pattern: {
              value: /^\d{1,2}(\.\d{1,2})?(\*\d{1,2}(\.\d{1,2})?)?$/,
              message: 'Enter a valid size (e.g., 6.50 or 5.50*7.90)'
            }
          })}
          error={errors.size_mm?.message}
          helperText="Accepted formats: Single size (e.g., 6.50) or dimensions (e.g., 5.50*7.90). Use * to separate dimensions."
          onKeyDown={(e) => {
            const allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab', '.', '*'];
            const isNumber = /^[0-9]$/.test(e.key);
            if (!isNumber && !allowedKeys.includes(e.key)) {
              e.preventDefault();
            }
          }}
        />

        <Input
          label="Carat Weight"
          type="number"
          step="0.01"
          required
          {...register('carat', {
            required: 'Carat weight is required',
            min: { value: 0.01, message: 'Carat weight must be greater than 0' },
            max: { value: 100, message: 'Carat weight must be less than 100' }
          })}
          error={errors.carat?.message}
        />

        <Select
          label="Clarity"
          options={clarityOptions}
          required
          {...register('clarity', { required: 'Clarity is required' })}
          error={errors.clarity?.message}
        />

        <Select
          label="Color"
          options={colorOptions}
          required
          {...register('color', { required: 'Color is required' })}
          error={errors.color?.message}
        />

        <Input
          label="Certificate No."
          type="text"
          required
          {...register('certificate_no', {
            required: 'Certificate number is required',
            pattern: {
              value: /^[a-zA-Z0-9\-]{3,30}$/,
              message: 'Certificate number must be 3-30 alphanumeric characters'
            }
          })}
          error={errors.certificate_no?.message}
          helperText="Alphanumeric, 3-30 characters. Enter the official certificate number from the lab or supplier."
        />

        <Input
          label="Quantity"
          type="number"
          required
          {...register('quantity', {
            required: 'Quantity is required',
            min: { value: 1, message: 'Quantity must be at least 1' }
          })}
          error={errors.quantity?.message}
        />

        <Input
          label="Purchase Date"
          type="date"
          required
          {...register('purchase_date', { required: 'Purchase date is required' })}
          error={errors.purchase_date?.message}
        />

        <Select
          label="Status"
          options={statusOptions}
          required
          {...register('status', { required: 'Status is required' })}
          error={errors.status?.message}
        />

        <Select
          label="Vendor"
          options={vendorOptions || []}
          disabled={isLoadingVendors}
          {...register('vendor_id')}
          error={errors.vendor_id?.message}
          placeholder="Select vendor (optional)"
        />
      </div>

      <div className="flex justify-end space-x-4">
        <Button
          type="submit"
          isLoading={mutation.isPending}
        >
          {isEditing ? 'Update Diamond' : 'Create Diamond'}
        </Button>
      </div>

      {/* Add Shape Modal */}
      <Modal isOpen={showAddShapeModal} onClose={() => { setShowAddShapeModal(false); setAddShapeError(null); }} title="Add New Shape">
        <div className="space-y-4">
          <Input
            label="Shape Name"
            value={newShapeName}
            onChange={e => setNewShapeName(e.target.value)}
            ref={inputRef}
            required
            error={addShapeError || undefined}
            autoFocus
            maxLength={50}
          />
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="ghost" onClick={() => { setShowAddShapeModal(false); setAddShapeError(null); }}>
              Cancel
            </Button>
            <Button
              type="button"
              isLoading={addShapeMutation.isPending}
              disabled={!newShapeName.trim() || addShapeMutation.isPending}
              onClick={() => {
                if (newShapeName.trim()) {
                  addShapeMutation.mutate(newShapeName.trim());
                }
              }}
            >
              Add
            </Button>
          </div>
        </div>
      </Modal>
    </form>
  );
};

export default DiamondForm;