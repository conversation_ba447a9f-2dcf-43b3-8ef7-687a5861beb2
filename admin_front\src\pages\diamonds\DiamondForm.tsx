import React from 'react';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery } from '@tanstack/react-query';
import { api } from '../../lib/api';
import { Diamond } from '../../types';
import Input from '../../components/ui/Input';
import Select from '../../components/ui/Select';
import Button from '../../components/ui/Button';
import Modal from '../../components/ui/Modal';
import toast from 'react-hot-toast';
import {
  DIAMOND_COLORS,
  DIAMOND_CLARITIES,
  CUT_GRADES,
  POLISH_SYMMETRY_GRADES,
  FLUORESCENCE_INTENSITIES,
  FLUORESCENCE_COLORS,
  DIAMOND_STATUSES,
  CERTIFICATION_LABS,
  CULET_SIZES,
  GIRDLE_DESCRIPTIONS,
  VALIDATION_RANGES
} from '../../constants/diamond';

interface DiamondFormProps {
  diamond?: Diamond;
  onSuccess: () => void;
}

interface DiamondFormData {
  // Basic Properties
  shape_id: string;
  carat: number;

  // 4Cs - Industry Standard
  color: string;
  clarity: string;
  cut_grade?: string;

  // Additional Grading
  polish?: string;
  symmetry?: string;
  fluorescence?: string;
  fluorescence_color?: string;

  // Measurements
  length_mm?: number;
  width_mm?: number;
  depth_mm?: number;
  depth_percent?: number;
  table_percent?: number;
  girdle?: string;
  culet?: string;

  // Certification
  certificate_no: string;
  certification_lab?: string;
  certificate_date?: string;
  certificate_url?: string;

  // Pricing
  cost_price?: number;
  retail_price?: number;
  market_value?: number;
  last_valuation_date?: string;

  // Inventory
  quantity: number;
  reserved_quantity?: number;
  minimum_stock?: number;

  // Status and Location
  status: 'in_stock' | 'reserved' | 'sold' | 'manufacturing' | 'damaged' | 'lost';
  location?: string;
  notes?: string;

  // Relationships
  vendor_id?: string;

  // Dates
  purchase_date: string;

  // Legacy
  size_mm?: string;
}

const DiamondForm: React.FC<DiamondFormProps> = ({ diamond, onSuccess }) => {
  const [showAddShapeModal, setShowAddShapeModal] = React.useState(false);
  const [newShapeName, setNewShapeName] = React.useState('');
  const [shapeOptionsState, setShapeOptionsState] = React.useState<{ value: string; label: string }[]>([]);
  const isEditing = !!diamond;
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset
  } = useForm<DiamondFormData>({
    defaultValues: diamond ? {
      shape_id: diamond.shape_id?.toString() || '',
      carat: diamond.carat,
      color: diamond.color,
      clarity: diamond.clarity,
      cut_grade: diamond.cut_grade || '',
      polish: diamond.polish || '',
      symmetry: diamond.symmetry || '',
      fluorescence: diamond.fluorescence || '',
      fluorescence_color: diamond.fluorescence_color || '',
      length_mm: diamond.length_mm,
      width_mm: diamond.width_mm,
      depth_mm: diamond.depth_mm,
      depth_percent: diamond.depth_percent,
      table_percent: diamond.table_percent,
      girdle: diamond.girdle || '',
      culet: diamond.culet || '',
      certificate_no: diamond.certificate_no,
      certification_lab: diamond.certification_lab || '',
      certificate_date: diamond.certificate_date
        ? (diamond.certificate_date.includes('T') ? diamond.certificate_date.split('T')[0] : diamond.certificate_date)
        : '',
      certificate_url: diamond.certificate_url || '',
      cost_price: diamond.cost_price,
      retail_price: diamond.retail_price,
      market_value: diamond.market_value,
      last_valuation_date: diamond.last_valuation_date
        ? (diamond.last_valuation_date.includes('T') ? diamond.last_valuation_date.split('T')[0] : diamond.last_valuation_date)
        : '',
      quantity: diamond.quantity,
      reserved_quantity: diamond.reserved_quantity,
      minimum_stock: diamond.minimum_stock,
      status: diamond.status,
      location: diamond.location || '',
      notes: diamond.notes || '',
      vendor_id: diamond.vendor_id?.toString() || '',
      purchase_date: diamond.purchase_date
        ? (diamond.purchase_date.includes('T') ? diamond.purchase_date.split('T')[0] : diamond.purchase_date)
        : '',
      size_mm: diamond.size_mm || ''
    } : {
      quantity: 1,
      minimum_stock: 1,
      status: 'in_stock' as const
    }
  });

  // Ensure form fields update when editing a different diamond
  React.useEffect(() => {
    if (diamond) {
      reset({
        shape_id: diamond.shape_id?.toString() || '',
        carat: diamond.carat,
        color: diamond.color,
        clarity: diamond.clarity,
        cut_grade: diamond.cut_grade || '',
        polish: diamond.polish || '',
        symmetry: diamond.symmetry || '',
        fluorescence: diamond.fluorescence || '',
        fluorescence_color: diamond.fluorescence_color || '',
        length_mm: diamond.length_mm,
        width_mm: diamond.width_mm,
        depth_mm: diamond.depth_mm,
        depth_percent: diamond.depth_percent,
        table_percent: diamond.table_percent,
        girdle: diamond.girdle || '',
        culet: diamond.culet || '',
        certificate_no: diamond.certificate_no,
        certification_lab: diamond.certification_lab || '',
        certificate_date: diamond.certificate_date
          ? (diamond.certificate_date.includes('T') ? diamond.certificate_date.split('T')[0] : diamond.certificate_date)
          : '',
        certificate_url: diamond.certificate_url || '',
        cost_price: diamond.cost_price,
        retail_price: diamond.retail_price,
        market_value: diamond.market_value,
        last_valuation_date: diamond.last_valuation_date
          ? (diamond.last_valuation_date.includes('T') ? diamond.last_valuation_date.split('T')[0] : diamond.last_valuation_date)
          : '',
        quantity: diamond.quantity,
        reserved_quantity: diamond.reserved_quantity,
        minimum_stock: diamond.minimum_stock,
        status: diamond.status,
        location: diamond.location || '',
        notes: diamond.notes || '',
        vendor_id: diamond.vendor_id?.toString() || '',
        purchase_date: diamond.purchase_date
          ? (diamond.purchase_date.includes('T') ? diamond.purchase_date.split('T')[0] : diamond.purchase_date)
          : '',
        size_mm: diamond.size_mm || ''
      });
    }
  }, [diamond, reset]);
  const mutation = useMutation({
    mutationFn: async (data: DiamondFormData) => {
      // Convert shape_id from string to number
      const payload = {
        ...data,
        shape_id: parseInt(data.shape_id)
      };
      
      if (isEditing) {
        await api.diamonds.update(diamond.id, payload);
      } else {
        await api.diamonds.create(payload);
      }
    },
    onSuccess: () => {
      toast.success(`Diamond ${isEditing ? 'updated' : 'created'} successfully`);
      onSuccess();
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || `Failed to ${isEditing ? 'update' : 'create'} diamond`;
      toast.error(message);
    }
  });

  // Enhanced validation function
  const validateDiamondData = (data: DiamondFormData) => {
    const errors: string[] = [];

    // Carat validation
    if (data.carat < VALIDATION_RANGES.carat.min || data.carat > VALIDATION_RANGES.carat.max) {
      errors.push(`Carat must be between ${VALIDATION_RANGES.carat.min} and ${VALIDATION_RANGES.carat.max}`);
    }

    // Color validation
    if (data.color && !DIAMOND_COLORS.some(c => c.value === data.color)) {
      errors.push('Invalid color grade selected');
    }

    // Clarity validation
    if (data.clarity && !DIAMOND_CLARITIES.some(c => c.value === data.clarity)) {
      errors.push('Invalid clarity grade selected');
    }

    // Measurement validations
    if (data.length_mm && (data.length_mm < VALIDATION_RANGES.length_mm.min || data.length_mm > VALIDATION_RANGES.length_mm.max)) {
      errors.push(`Length must be between ${VALIDATION_RANGES.length_mm.min} and ${VALIDATION_RANGES.length_mm.max} mm`);
    }

    if (data.width_mm && (data.width_mm < VALIDATION_RANGES.width_mm.min || data.width_mm > VALIDATION_RANGES.width_mm.max)) {
      errors.push(`Width must be between ${VALIDATION_RANGES.width_mm.min} and ${VALIDATION_RANGES.width_mm.max} mm`);
    }

    if (data.depth_mm && (data.depth_mm < VALIDATION_RANGES.depth_mm.min || data.depth_mm > VALIDATION_RANGES.depth_mm.max)) {
      errors.push(`Depth must be between ${VALIDATION_RANGES.depth_mm.min} and ${VALIDATION_RANGES.depth_mm.max} mm`);
    }

    // Percentage validations
    if (data.depth_percent && (data.depth_percent < VALIDATION_RANGES.depth_percent.min || data.depth_percent > VALIDATION_RANGES.depth_percent.max)) {
      errors.push(`Depth percentage must be between ${VALIDATION_RANGES.depth_percent.min}% and ${VALIDATION_RANGES.depth_percent.max}%`);
    }

    if (data.table_percent && (data.table_percent < VALIDATION_RANGES.table_percent.min || data.table_percent > VALIDATION_RANGES.table_percent.max)) {
      errors.push(`Table percentage must be between ${VALIDATION_RANGES.table_percent.min}% and ${VALIDATION_RANGES.table_percent.max}%`);
    }

    // Price validations
    if (data.cost_price && data.cost_price < 0) {
      errors.push('Cost price cannot be negative');
    }

    if (data.retail_price && data.retail_price < 0) {
      errors.push('Retail price cannot be negative');
    }

    if (data.cost_price && data.retail_price && data.cost_price > data.retail_price) {
      errors.push('Cost price should not exceed retail price');
    }

    // Quantity validations
    if (data.quantity < 0) {
      errors.push('Quantity cannot be negative');
    }

    if (data.reserved_quantity && data.reserved_quantity > data.quantity) {
      errors.push('Reserved quantity cannot exceed total quantity');
    }

    return errors;
  };

  const onSubmit = (data: DiamondFormData) => {
    // Client-side validation
    const validationErrors = validateDiamondData(data);
    if (validationErrors.length > 0) {
      validationErrors.forEach(error => toast.error(error));
      return;
    }

    // Convert vendor_id to number if provided
    let vendor_id: number | undefined = undefined;
    if (data.vendor_id !== undefined && data.vendor_id !== '' && !isNaN(Number(data.vendor_id))) {
      vendor_id = Number(data.vendor_id);
    }

    // Prepare payload with proper data types
    const payload = {
      ...data,
      vendor_id: vendor_id?.toString(),
      // Ensure numeric fields are properly typed
      carat: Number(data.carat),
      length_mm: data.length_mm ? Number(data.length_mm) : undefined,
      width_mm: data.width_mm ? Number(data.width_mm) : undefined,
      depth_mm: data.depth_mm ? Number(data.depth_mm) : undefined,
      depth_percent: data.depth_percent ? Number(data.depth_percent) : undefined,
      table_percent: data.table_percent ? Number(data.table_percent) : undefined,
      cost_price: data.cost_price ? Number(data.cost_price) : undefined,
      retail_price: data.retail_price ? Number(data.retail_price) : undefined,
      market_value: data.market_value ? Number(data.market_value) : undefined,
      quantity: Number(data.quantity),
      reserved_quantity: data.reserved_quantity ? Number(data.reserved_quantity) : undefined,
      minimum_stock: data.minimum_stock ? Number(data.minimum_stock) : undefined
    };

    mutation.mutate(payload);
  };

  // Fetch shapes and keep in local state for immediate refresh after add
  const { data: shapeOptionsRaw = [], isLoading: isLoadingShapes, isError: isErrorShapes, refetch: refetchShapes } = useQuery({
    queryKey: ['shapes'],
    queryFn: async () => {
      const response = await api.shapes.list();
      // Support both array and {data: array} for compatibility
      const arr = Array.isArray(response) ? response : response?.data;
      if (!Array.isArray(arr)) throw new Error('Failed to fetch shapes');
      // Sort alphabetically for UX
      return arr
        .map((shape: { id: number; name: string }) => ({
          value: shape.id.toString(),
          label: shape.name
        }))
        .sort((a, b) => a.label.localeCompare(b.label));
    },
  });
  React.useEffect(() => {
    setShapeOptionsState(Array.isArray(shapeOptionsRaw) ? shapeOptionsRaw : []);
  }, [shapeOptionsRaw]);

  const { data: vendorOptions, isLoading: isLoadingVendors } = useQuery({
    queryKey: ['vendors'],
    queryFn: async () => {
      const response = await api.vendors.list();
      const arr = Array.isArray(response) ? response : response?.data;
      if (!Array.isArray(arr)) throw new Error('Failed to fetch vendors');
      return arr.map((vendor: { id: number; name: string }) => ({
        value: vendor.id.toString(),
        label: vendor.name
      }));
    },
  });

  const clarityOptions = [
    { value: 'FL', label: 'FL (Flawless)' },
    { value: 'IF', label: 'IF (Internally Flawless)' },
    { value: 'VVS1', label: 'VVS1' },
    { value: 'VVS2', label: 'VVS2' },
    { value: 'VS1', label: 'VS1' },
    { value: 'VS2', label: 'VS2' },
    { value: 'SI1', label: 'SI1' },
    { value: 'SI2', label: 'SI2' },
    { value: 'I1', label: 'I1' },
    { value: 'I2', label: 'I2' },
    { value: 'I3', label: 'I3' }
  ];

  const colorOptions = [
    { value: 'D', label: 'D (Colorless)' },
    { value: 'E', label: 'E (Colorless)' },
    { value: 'F', label: 'F (Colorless)' },
    { value: 'G', label: 'G (Near Colorless)' },
    { value: 'H', label: 'H (Near Colorless)' },
    { value: 'I', label: 'I (Near Colorless)' },
    { value: 'J', label: 'J (Near Colorless)' },
    { value: 'K', label: 'K (Faint Yellow)' },
    { value: 'L', label: 'L (Faint Yellow)' },
    { value: 'M', label: 'M (Faint Yellow)' }
  ];

  const statusOptions = [
    { value: 'in_stock', label: 'In Stock' },
    { value: 'reserved', label: 'Reserved' },
    { value: 'used', label: 'Used' }
  ];

  // Add shape handler
  const [addShapeError, setAddShapeError] = React.useState<string | null>(null);
  const inputRef = React.useRef<HTMLInputElement>(null);
  const addShapeMutation = useMutation({
    mutationFn: async (name: string) => {
      setAddShapeError(null);
      return await api.shapes.create({ name });
    },
    onSuccess: async (newShape) => {
      toast.success('Shape added successfully');
      setShowAddShapeModal(false);
      setNewShapeName('');
      await refetchShapes();
      // Auto-select the new shape
      if (newShape && newShape.id) {
        // Use requestAnimationFrame for better UI responsiveness
        requestAnimationFrame(() => {
          setValue('shape_id', newShape.id.toString());
        });
      }
      // Use requestAnimationFrame for focusing input
      requestAnimationFrame(() => inputRef.current?.focus());
    },
    onError: (error: any) => {
      let message = error?.response?.data?.message || 'Failed to add shape';
      if (error?.response?.status === 409) message = 'Shape name already exists.';
      setAddShapeError(message);
      toast.error(message);
    }
  });
  React.useEffect(() => {
    if (showAddShapeModal) setTimeout(() => inputRef.current?.focus(), 100);
  }, [showAddShapeModal]);

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
      {/* Basic Information Section */}
      <div className="bg-gray-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="flex flex-col space-y-1">
            <div className="flex items-center justify-between">
              <label className="font-medium">Shape</label>
              <Button type="button" variant="secondary" size="sm" onClick={() => setShowAddShapeModal(true)}>
                + Add Shape
              </Button>
            </div>
            <Select
              options={isLoadingShapes ? [] : shapeOptionsState}
              required
              disabled={isLoadingShapes || isErrorShapes}
              {...register('shape_id', { required: 'Shape is required' })}
              error={errors.shape_id?.message || (isErrorShapes ? 'Failed to load shapes' : undefined)}
            />
          </div>

          <Input
            label="Carat Weight"
            type="number"
            step="0.01"
            required
            {...register('carat', {
              required: 'Carat weight is required',
              min: { value: VALIDATION_RANGES.carat.min, message: `Carat weight must be at least ${VALIDATION_RANGES.carat.min}` },
              max: { value: VALIDATION_RANGES.carat.max, message: `Carat weight must be less than ${VALIDATION_RANGES.carat.max}` }
            })}
            error={errors.carat?.message}
            helperText="Enter the exact carat weight as certified"
          />
        </div>
      </div>

      {/* 4Cs Grading Section */}
      <div className="bg-blue-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">4Cs Grading (Industry Standards)</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Select
            label="Color Grade"
            options={DIAMOND_COLORS}
            required
            {...register('color', { required: 'Color grade is required' })}
            error={errors.color?.message}
            helperText="GIA color scale from D (colorless) to Z (light yellow)"
          />

          <Select
            label="Clarity Grade"
            options={DIAMOND_CLARITIES}
            required
            {...register('clarity', { required: 'Clarity grade is required' })}
            error={errors.clarity?.message}
            helperText="GIA clarity scale from FL (flawless) to I3 (included)"
          />

          <Select
            label="Cut Grade"
            options={CUT_GRADES}
            {...register('cut_grade')}
            error={errors.cut_grade?.message}
            placeholder="Select cut grade (optional)"
            helperText="Overall cut quality assessment"
          />
        </div>
      </div>

      {/* Additional Grading Section */}
      <div className="bg-green-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Additional Grading</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Select
            label="Polish"
            options={POLISH_SYMMETRY_GRADES}
            {...register('polish')}
            error={errors.polish?.message}
            placeholder="Select polish grade"
            helperText="Surface finish quality"
          />

          <Select
            label="Symmetry"
            options={POLISH_SYMMETRY_GRADES}
            {...register('symmetry')}
            error={errors.symmetry?.message}
            placeholder="Select symmetry grade"
            helperText="Facet alignment quality"
          />

          <Select
            label="Fluorescence"
            options={FLUORESCENCE_INTENSITIES}
            {...register('fluorescence')}
            error={errors.fluorescence?.message}
            placeholder="Select fluorescence"
            helperText="UV light reaction intensity"
          />

          <Select
            label="Fluorescence Color"
            options={FLUORESCENCE_COLORS}
            {...register('fluorescence_color')}
            error={errors.fluorescence_color?.message}
            placeholder="Select fluorescence color"
            helperText="Color under UV light"
          />

          <Select
            label="Girdle"
            options={GIRDLE_DESCRIPTIONS}
            {...register('girdle')}
            error={errors.girdle?.message}
            placeholder="Select girdle description"
            helperText="Girdle thickness"
          />

          <Select
            label="Culet"
            options={CULET_SIZES}
            {...register('culet')}
            error={errors.culet?.message}
            placeholder="Select culet size"
            helperText="Bottom facet size"
          />
        </div>
      </div>

      {/* Measurements Section */}
      <div className="bg-yellow-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Measurements</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Input
            label="Length (mm)"
            type="number"
            step="0.01"
            {...register('length_mm', {
              min: { value: VALIDATION_RANGES.length_mm.min, message: `Length must be at least ${VALIDATION_RANGES.length_mm.min}mm` },
              max: { value: VALIDATION_RANGES.length_mm.max, message: `Length must be less than ${VALIDATION_RANGES.length_mm.max}mm` }
            })}
            error={errors.length_mm?.message}
            helperText="Precise measurement in mm"
          />

          <Input
            label="Width (mm)"
            type="number"
            step="0.01"
            {...register('width_mm', {
              min: { value: VALIDATION_RANGES.width_mm.min, message: `Width must be at least ${VALIDATION_RANGES.width_mm.min}mm` },
              max: { value: VALIDATION_RANGES.width_mm.max, message: `Width must be less than ${VALIDATION_RANGES.width_mm.max}mm` }
            })}
            error={errors.width_mm?.message}
            helperText="Precise measurement in mm"
          />

          <Input
            label="Depth (mm)"
            type="number"
            step="0.01"
            {...register('depth_mm', {
              min: { value: VALIDATION_RANGES.depth_mm.min, message: `Depth must be at least ${VALIDATION_RANGES.depth_mm.min}mm` },
              max: { value: VALIDATION_RANGES.depth_mm.max, message: `Depth must be less than ${VALIDATION_RANGES.depth_mm.max}mm` }
            })}
            error={errors.depth_mm?.message}
            helperText="Precise measurement in mm"
          />

          <Input
            label="Depth %"
            type="number"
            step="0.1"
            {...register('depth_percent', {
              min: { value: VALIDATION_RANGES.depth_percent.min, message: `Depth % must be at least ${VALIDATION_RANGES.depth_percent.min}%` },
              max: { value: VALIDATION_RANGES.depth_percent.max, message: `Depth % must be less than ${VALIDATION_RANGES.depth_percent.max}%` }
            })}
            error={errors.depth_percent?.message}
            helperText="Depth as percentage of width"
          />

          <Input
            label="Table %"
            type="number"
            step="0.1"
            {...register('table_percent', {
              min: { value: VALIDATION_RANGES.table_percent.min, message: `Table % must be at least ${VALIDATION_RANGES.table_percent.min}%` },
              max: { value: VALIDATION_RANGES.table_percent.max, message: `Table % must be less than ${VALIDATION_RANGES.table_percent.max}%` }
            })}
            error={errors.table_percent?.message}
            helperText="Table as percentage of width"
          />

          <Input
            label="Legacy Size (mm)"
            type="text"
            placeholder="e.g., 6.50 or 5.50*7.90"
            {...register('size_mm')}
            error={errors.size_mm?.message}
            helperText="Legacy field - use measurements above instead"
          />
        </div>
      </div>

      {/* Certification Section */}
      <div className="bg-purple-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Certification</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Input
            label="Certificate Number"
            type="text"
            required
            {...register('certificate_no', {
              required: 'Certificate number is required',
              pattern: {
                value: /^[a-zA-Z0-9\-]{3,50}$/,
                message: 'Certificate number must be 3-50 alphanumeric characters'
              }
            })}
            error={errors.certificate_no?.message}
            helperText="Official certificate number from the lab"
          />

          <Select
            label="Certification Lab"
            options={CERTIFICATION_LABS}
            {...register('certification_lab')}
            error={errors.certification_lab?.message}
            placeholder="Select certification lab"
            helperText="Grading laboratory"
          />

          <Input
            label="Certificate Date"
            type="date"
            {...register('certificate_date')}
            error={errors.certificate_date?.message}
            helperText="Date when certificate was issued"
          />

          <Input
            label="Certificate URL"
            type="url"
            {...register('certificate_url')}
            error={errors.certificate_url?.message}
            placeholder="https://..."
            helperText="Link to online certificate verification"
          />
        </div>
      </div>

      {/* Pricing Section */}
      <div className="bg-red-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Pricing</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Input
            label="Cost Price"
            type="number"
            step="0.01"
            {...register('cost_price', {
              min: { value: 0, message: 'Cost price must be non-negative' }
            })}
            error={errors.cost_price?.message}
            helperText="Purchase cost from supplier"
          />

          <Input
            label="Retail Price"
            type="number"
            step="0.01"
            {...register('retail_price', {
              min: { value: 0, message: 'Retail price must be non-negative' }
            })}
            error={errors.retail_price?.message}
            helperText="Selling price to customers"
          />

          <Input
            label="Market Value"
            type="number"
            step="0.01"
            {...register('market_value', {
              min: { value: 0, message: 'Market value must be non-negative' }
            })}
            error={errors.market_value?.message}
            helperText="Current market valuation"
          />

          <Input
            label="Last Valuation Date"
            type="date"
            {...register('last_valuation_date')}
            error={errors.last_valuation_date?.message}
            helperText="Date of last market valuation"
          />
        </div>
      </div>

      {/* Inventory Management Section */}
      <div className="bg-indigo-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Inventory Management</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Input
            label="Quantity"
            type="number"
            required
            {...register('quantity', {
              required: 'Quantity is required',
              min: { value: 0, message: 'Quantity must be non-negative' }
            })}
            error={errors.quantity?.message}
            helperText="Total quantity in stock"
          />

          <Input
            label="Reserved Quantity"
            type="number"
            {...register('reserved_quantity', {
              min: { value: 0, message: 'Reserved quantity must be non-negative' }
            })}
            error={errors.reserved_quantity?.message}
            helperText="Quantity reserved for customers"
          />

          <Input
            label="Minimum Stock"
            type="number"
            {...register('minimum_stock', {
              min: { value: 0, message: 'Minimum stock must be non-negative' }
            })}
            error={errors.minimum_stock?.message}
            helperText="Reorder alert threshold"
          />

          <Select
            label="Status"
            options={DIAMOND_STATUSES}
            required
            {...register('status', { required: 'Status is required' })}
            error={errors.status?.message}
            helperText="Current diamond status"
          />

          <Input
            label="Location"
            type="text"
            {...register('location')}
            error={errors.location?.message}
            placeholder="e.g., Vault A, Shelf 3"
            helperText="Storage location"
          />

          <Select
            label="Vendor"
            options={vendorOptions || []}
            disabled={isLoadingVendors}
            {...register('vendor_id')}
            error={errors.vendor_id?.message}
            placeholder="Select vendor (optional)"
            helperText="Supplier/vendor"
          />
        </div>
      </div>

      {/* Additional Information Section */}
      <div className="bg-gray-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Additional Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-1 gap-6">
          <Input
            label="Purchase Date"
            type="date"
            required
            {...register('purchase_date', { required: 'Purchase date is required' })}
            error={errors.purchase_date?.message}
            helperText="Date when diamond was purchased"
          />

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              {...register('notes')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Additional notes about this diamond..."
            />
            {errors.notes && (
              <p className="mt-1 text-sm text-red-600">{errors.notes.message}</p>
            )}
          </div>
        </div>
      </div>

      <div className="flex justify-end space-x-4">
        <Button
          type="submit"
          isLoading={mutation.isPending}
        >
          {isEditing ? 'Update Diamond' : 'Create Diamond'}
        </Button>
      </div>

      {/* Add Shape Modal */}
      <Modal isOpen={showAddShapeModal} onClose={() => { setShowAddShapeModal(false); setAddShapeError(null); }} title="Add New Shape">
        <div className="space-y-4">
          <Input
            label="Shape Name"
            value={newShapeName}
            onChange={e => setNewShapeName(e.target.value)}
            ref={inputRef}
            required
            error={addShapeError || undefined}
            autoFocus
            maxLength={50}
          />
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="ghost" onClick={() => { setShowAddShapeModal(false); setAddShapeError(null); }}>
              Cancel
            </Button>
            <Button
              type="button"
              isLoading={addShapeMutation.isPending}
              disabled={!newShapeName.trim() || addShapeMutation.isPending}
              onClick={() => {
                if (newShapeName.trim()) {
                  addShapeMutation.mutate(newShapeName.trim());
                }
              }}
            >
              Add
            </Button>
          </div>
        </div>
      </Modal>
    </form>
  );
};

export default DiamondForm;