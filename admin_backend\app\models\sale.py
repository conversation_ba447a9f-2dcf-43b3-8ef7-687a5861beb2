from app import db
from datetime import date

class Sale(db.Model):
    __tablename__ = 'sales'
    id = db.Column(db.Integer, primary_key=True)
    invoice_no = db.Column(db.String(50), unique=True, nullable=False)
    customer_name = db.Column(db.String(100), nullable=False)
    sale_date = db.Column(db.Date, default=date.today)
    total_amount = db.Column(db.Float, nullable=False)
    payment_status = db.Column(db.String(20), default='unpaid')
    jewelry_id = db.Column(db.Integer, db.<PERSON>Key('jewelry_items.id', name='fk_sales_jewelry_id'), nullable=False)
    created_at = db.Column(db.DateTime, default=db.func.now())
    jewelry_item = db.relationship('JewelryItem', backref='sales')
