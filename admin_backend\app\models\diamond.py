from app import db
from datetime import date
from sqlalchemy.orm import relationship

class Shape(db.Model):
    __tablename__ = 'shapes'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False, unique=True)
    
    @classmethod
    def create_default_shapes(cls):
        """Create default shapes if they don't exist."""
        predefined_shapes = [
            'Round', 'Princess', 'Cushion', 'Emerald',
            'Oval', 'Pear', '<PERSON><PERSON>', '<PERSON><PERSON>',
            'Radiant', 'Heart', 'Trillion', 'Baguette',
            'Portugal', 'Old Mine', 'Old European', 'Rose',
            'Hexagon', 'Octagon', 'Briolette', 'Tapered Baguette',
            'Bullet', 'Kite', 'Shield', 'Half Moon',
            'Lozenge', 'Triangle', 'Fan', 'Trapezoid',
            'French Cut', 'Cabochon', 'Star', 'Horse Head',
            'Navette', 'Antique Cushion', 'Bead'
        ]

        for shape_name in predefined_shapes:
            if not cls.query.filter_by(name=shape_name).first():
                shape = cls(name=shape_name)
                db.session.add(shape)

        db.session.commit()

class Manufacturing(db.Model):
    __tablename__ = 'manufacturing'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    diamonds = relationship('Diamond', backref='manufacturing')

class Jewelry(db.Model):
    __tablename__ = 'jewelry'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    diamonds = relationship('Diamond', backref='jewelry')

class Diamond(db.Model):
    __tablename__ = 'diamonds'
    id = db.Column(db.Integer, primary_key=True)
    shape_id = db.Column(db.Integer, db.ForeignKey('shapes.id'), nullable=False)
    shape = relationship('Shape', backref='diamonds')
    manufacturing_id = db.Column(db.Integer, db.ForeignKey('manufacturing.id'))
    jewelry_id = db.Column(db.Integer, db.ForeignKey('jewelry.id'))
    vendor_id = db.Column(db.Integer, db.ForeignKey('vendors.id'), nullable=True)
    vendor = db.relationship('Vendor', backref='diamonds')
    size_mm = db.Column(db.String(50), nullable=False)  # Updated to String to handle both formats
    carat = db.Column(db.Float, nullable=False)
    clarity = db.Column(db.String(20), nullable=False)
    color = db.Column(db.String(20), nullable=False)
    certificate_no = db.Column(db.String(100), unique=True, nullable=False)
    quantity = db.Column(db.Integer, nullable=False, default=1)
    purchase_date = db.Column(db.Date, default=date.today)
    status = db.Column(db.String(20), default='in_stock')

# Ensure `db` is correctly initialized in the app context.
