#!/usr/bin/env python3
"""
Debug vendor creation issue
"""

import requests
import time
import random

BASE_URL = "http://localhost:8000/api"

def debug_vendor():
    print("🔍 Debugging Vendor Creation")
    print("=" * 30)
    
    # Login
    response = requests.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "<PERSON><PERSON>@109"
    })
    
    if response.status_code != 200:
        print("❌ Login failed")
        return
    
    token = response.json().get('access_token')
    headers = {'Authorization': f'Bearer {token}'}
    print("✅ Authenticated")
    
    # Check existing vendors
    response = requests.get(f"{BASE_URL}/vendors", headers=headers)
    print(f"\nExisting vendors: {response.status_code}")
    if response.status_code == 200:
        vendors = response.json()
        print(f"Found {len(vendors)} vendors")
        for vendor in vendors:
            print(f"  - {vendor.get('name')} (GST: {vendor.get('gst_number')})")
    
    # Try creating vendor with minimal data
    unique_id = int(time.time()) + random.randint(10000, 99999)
    minimal_vendor = {
        "name": f"Minimal Vendor {unique_id}",
        "gst_number": f"29AAAAA{unique_id % 100000:05d}A1Z5",
        "contact_number": "+91-9876543210",
        "address": "Test Address"
    }
    
    print(f"\nTrying to create vendor with GST: {minimal_vendor['gst_number']}")
    response = requests.post(f"{BASE_URL}/vendors", json=minimal_vendor, headers=headers)
    print(f"Create vendor: {response.status_code}")
    
    if response.status_code in [200, 201]:
        vendor = response.json()
        print(f"✅ Success! Created vendor ID: {vendor.get('id')}")
        
        # Test the vendor
        vendor_id = vendor.get('id')
        
        # Test READ
        response = requests.get(f"{BASE_URL}/vendors/{vendor_id}", headers=headers)
        print(f"Read vendor: {response.status_code}")
        
        # Test UPDATE
        update_data = {"name": f"Updated Vendor {unique_id}"}
        response = requests.put(f"{BASE_URL}/vendors/{vendor_id}", json=update_data, headers=headers)
        print(f"Update vendor: {response.status_code}")
        
        # Test DELETE
        response = requests.delete(f"{BASE_URL}/vendors/{vendor_id}", headers=headers)
        print(f"Delete vendor: {response.status_code}")
        
    else:
        print(f"❌ Failed: {response.text}")
        
        # Try with different GST format
        print("\nTrying different GST format...")
        minimal_vendor['gst_number'] = f"27BBBBB{unique_id % 100000:05d}B1Z6"
        response = requests.post(f"{BASE_URL}/vendors", json=minimal_vendor, headers=headers)
        print(f"Create vendor (alt GST): {response.status_code}")
        
        if response.status_code not in [200, 201]:
            print(f"❌ Still failed: {response.text[:300]}")

if __name__ == "__main__":
    debug_vendor()
