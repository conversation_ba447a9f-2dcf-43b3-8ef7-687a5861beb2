{"data_mtime": 1753074106, "dep_lines": [3, 5, 7, 180, 6, 2, 4, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 20, 5, 5, 5, 5, 30, 30, 30, 30, 5], "dependencies": ["app.models.vendor", "app.utils.decorators", "app.models.jewelry", "app.models.manufacturing", "sqlalchemy.exc", "flask", "app", "builtins", "_frozen_importlib", "abc", "app.utils", "typing"], "hash": "45d70a7a293ade60a296988ba66ebab942fafcd0", "id": "app.api.vendor", "ignore_all": true, "interface_hash": "488c9ef2a0564826dc74932a2032925743c9db11", "mtime": 1753041351, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\admin_panel\\admin_backend\\app\\api\\vendor.py", "plugin_data": null, "size": 8455, "suppressed": ["flask_restx"], "version_id": "1.15.0"}