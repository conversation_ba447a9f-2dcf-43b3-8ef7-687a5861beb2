from app import db

class Vendor(db.Model):
    __tablename__ = 'vendors'
    __table_args__ = {'extend_existing': True}
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    gst_number = db.Column(db.String(30), unique=True, nullable=False, index=True)
    contact_number = db.Column(db.String(20), nullable=False)
    address = db.Column(db.String(255), nullable=False)
    # Relationship to manufacturing requests
