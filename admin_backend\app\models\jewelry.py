from app import db
from datetime import date

# Association table for jewelry-diamond with quantity
jewelry_diamonds = db.Table(
    'jewelry_diamonds',
    db.Column('jewelry_id', db.Integer, db.<PERSON><PERSON>('jewelry_items.id', name='fk_jewelry_diamonds_jewelry_id'), primary_key=True),
    db.Column('diamond_id', db.<PERSON>ger, db.<PERSON>('diamonds.id', name='fk_jewelry_diamonds_diamond_id'), primary_key=True),
    db.Column('quantity', db.Integer, nullable=False, default=1)
)

class JewelryItem(db.Model):
    __tablename__ = 'jewelry_items'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    design_code = db.Column(db.String(50), nullable=False)
    vendor_id = db.Column(db.Integer, db.ForeignKey('vendors.id', name='fk_jewelry_vendor_id'), nullable=False)
    gross_weight = db.Column(db.Float, nullable=False)
    metal_type = db.Column(db.String(30), nullable=False)
    received_date = db.Column(db.Date, default=date.today)
    status = db.Column(db.String(20), default='in_stock')
    image_path = db.Column(db.String(255), nullable=True)
    created_at = db.Column(db.DateTime, default=db.func.now())
    diamonds = db.relationship('Diamond', secondary=jewelry_diamonds, backref='jewelry_items')
    vendor = db.relationship('Vendor', backref='jewelry_items')
    # Relationship to vendor
