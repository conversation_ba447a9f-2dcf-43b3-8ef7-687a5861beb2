import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus, Edit, Trash2, Minus, Link2 } from 'lucide-react';
import { api } from '../../lib/api';
import { Diamond } from '../../types';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';
import Select from '../../components/ui/Select';
import Modal from '../../components/ui/Modal';
import { LoadingState, ErrorState, EmptyState } from '../../components/DataStates';
import DiamondForm from './DiamondForm';
import DeductStockForm from './DeductStockForm';
import toast from 'react-hot-toast';
import { saveAs } from 'file-saver';
import { format } from 'date-fns';

// Utility to convert array of objects to CSV
function diamondsToCSV(diamonds: Diamond[]): string {
  const headers = [
    'ID', 'Shape', 'Size (mm)', 'Carat', 'Clarity', 'Color', 'Certificate No.', 'Quantity', 'Purchase Date', 'Status', 'Vendor'
  ];
  const rows = diamonds.map(d => [
    d.id,
    d.shape,
    d.size_mm,
    d.carat,
    d.clarity,
    d.color,
    d.certificate_no,
    d.quantity,
    d.purchase_date,
    d.status,
    d.vendorName || ''
  ]);
  return [headers, ...rows].map(r => r.map(x => `"${x ?? ''}"`).join(',')).join('\n');
}

const DiamondList: React.FC = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeductModalOpen, setIsDeductModalOpen] = useState(false);
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);
  const [selectedDiamond, setSelectedDiamond] = useState<Diamond | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [shapeFilter, setShapeFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [selectedManufacturing, setSelectedManufacturing] = useState<number | null>(null);
  const [selectedJewelry, setSelectedJewelry] = useState<number | null>(null);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [isDeductConfirmOpen, setIsDeductConfirmOpen] = useState(false);
  const [pendingDeductDiamond, setPendingDeductDiamond] = useState<Diamond | null>(null);
  const [caratMin, setCaratMin] = useState('');
  const [caratMax, setCaratMax] = useState('');
  const [vendorFilter, setVendorFilter] = useState('');
  const [sortBy, setSortBy] = useState('');
  const [sortDir, setSortDir] = useState<'asc' | 'desc'>('asc');

  const queryClient = useQueryClient();

  // Debounce search input
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearch(searchTerm);
    }, 600); // 500ms debounce
    return () => clearTimeout(handler);
  }, [searchTerm]);

  // Enhanced error handling for diamond fetching
  const { 
    data: diamondsRaw, 
    isLoading, 
    error,
    refetch 
  } = useQuery({
    queryKey: ['diamonds', { search: debouncedSearch, shape: shapeFilter, status: statusFilter, page, limit: pageSize, min_carat: caratMin, max_carat: caratMax, vendor_id: vendorFilter, sort_by: sortBy, sort_dir: sortDir }],
    queryFn: async (): Promise<{ data: Diamond[]; total: number; page: number; limit: number }> => {
      const params: any = {};
      if (debouncedSearch && debouncedSearch.length > 1) params.search = debouncedSearch;
      if (shapeFilter) params.shape = shapeFilter;
      if (statusFilter) params.status = statusFilter;
      params.page = page;
      params.limit = pageSize;
      if (caratMin) params.min_carat = caratMin;
      if (caratMax) params.max_carat = caratMax;
      if (vendorFilter) params.vendor_id = vendorFilter;
      if (sortBy) {
        params.sort_by = sortBy;
        params.sort_dir = sortDir;
      }
      try {
        const response = await api.diamonds.list(params);
        if (!response || !response.data) {
          return { data: [], total: 0, page: 1, limit: pageSize };
        }
        if (Array.isArray(response.data)) {
          return { data: response.data, total: response.data.length, page: 1, limit: pageSize };
        }
        if (Array.isArray(response.data.data)) {
          return response.data;
        }
        return { data: [], total: 0, page: 1, limit: pageSize };
      } catch (err: any) {
        if (err.response?.status === 401) {
          toast.error('Unauthorized: Please log in again.');
        } else {
          toast.error('Failed to load diamonds. Please try again later.');
        }
        throw err;
      }
    },
    retry: 2
  });

  // Enhanced error handling for API calls
  const deleteMutation = useMutation({
    mutationFn: async (id: number) => {
      await api.diamonds.delete(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['diamonds'] });
      toast.success('Diamond deleted successfully');
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || 'Failed to delete diamond';
      toast.error(`Error: ${errorMessage}`);
    }
  });

  const assignManufacturingMutation = useMutation({
    mutationFn: async () => {
      if (!selectedDiamond || !selectedManufacturing) {
        throw new Error('Please select both diamond and manufacturing');
      }
      try {
        await api.diamonds.assignToManufacturing(selectedDiamond.id, selectedManufacturing);
      } catch (error: any) {
        if (error.response?.status === 404) {
          throw new Error('Manufacturing ID not found. Please select a valid manufacturing option.');
        }
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['diamonds'] });
      toast.success('Diamond assigned to manufacturing successfully');
      setIsAssignModalOpen(false);
      setSelectedDiamond(null);
      setSelectedManufacturing(null);
    },
    onError: (error: any) => {
      const errorMessage = error.message || 'Failed to assign diamond to manufacturing';
      toast.error(errorMessage);
    },
  });

  const assignJewelryMutation = useMutation({
    mutationFn: async () => {
      if (!selectedDiamond || !selectedJewelry) {
        throw new Error('Please select both diamond and jewelry');
      }
      await api.diamonds.assignToJewelry(selectedDiamond.id, selectedJewelry);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['diamonds'] });
      toast.success('Diamond assigned to jewelry successfully');
      setIsAssignModalOpen(false);
      setSelectedDiamond(null);
      setSelectedJewelry(null);
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to assign diamond to jewelry';
      toast.error(errorMessage);
    },
  });

  const handleEdit = (diamond: Diamond) => {
    setSelectedDiamond(diamond);
    setIsEditModalOpen(true);
  };

  const handleDeduct = (diamond: Diamond) => {
    setPendingDeductDiamond(diamond);
    setIsDeductConfirmOpen(true);
  };

  const confirmDeduct = () => {
    if (pendingDeductDiamond) {
      setSelectedDiamond(pendingDeductDiamond);
      setIsDeductModalOpen(true);
    }
    setIsDeductConfirmOpen(false);
    setPendingDeductDiamond(null);
  };

  const cancelDeduct = () => {
    setIsDeductConfirmOpen(false);
    setPendingDeductDiamond(null);
  };

  const handleAssign = (diamond: Diamond) => {
    setSelectedDiamond(diamond);
    setSelectedManufacturing(null);
    setSelectedJewelry(null);
    setIsAssignModalOpen(true);
  };

  const handleDelete = async (diamond: Diamond) => {
    if (window.confirm('Are you sure you want to delete this diamond? This action cannot be undone.')) {
      deleteMutation.mutate(diamond.id);
    }
  };

  const handleExportCSV = () => {
    if (!filteredDiamonds.length) {
      toast.error('No diamonds to export.');
      return;
    }
    const csv = diamondsToCSV(filteredDiamonds);
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    saveAs(blob, `diamonds_export_${new Date().toISOString().slice(0,10)}.csv`);
  };

  const { data: shapeOptions, isLoading: isLoadingShapes } = useQuery({
    queryKey: ['shapes'],
    queryFn: async () => {
      const response = await api.shapes.list();
      if (!response || !response.data) {
        throw new Error('Failed to fetch shapes');
      }
      return response.data.map((shape: { id: number; name: string }) => ({
        value: shape.id,
        label: shape.name
      }));
    },
  });

  const statusOptions = [
    { value: 'in_stock', label: 'In Stock' },
    { value: 'reserved', label: 'Reserved' },
    { value: 'used', label: 'Used' }
  ];

  const { data: vendorOptions, isLoading: isLoadingVendors } = useQuery({
    queryKey: ['vendors'],
    queryFn: async () => {
      const response = await api.vendors.list();
      const arr = Array.isArray(response) ? response : response?.data;
      if (!Array.isArray(arr)) throw new Error('Failed to fetch vendors');
      return arr.map((vendor: { id: number; name: string }) => ({
        value: vendor.id.toString(),
        label: vendor.name
      }));
    },
  });

  const { 
    data: manufacturingTypes
  } = useQuery({
    queryKey: ['manufacturing-types'],
    queryFn: async (): Promise<{ id: number; name: string }[]> => {
      const response = await api.manufacturing.list();
      if (!response) {
        throw new Error('Failed to fetch manufacturing types');
      }
      return response.data;
    },
    retry: 1
  });

  const { 
    data: jewelryOptions
  } = useQuery({
    queryKey: ['jewelry-options'],
    queryFn: async (): Promise<{ id: number; name: string }[]> => {
      const response = await api.jewelry.list({ status: 'in_stock' });
      if (!response) {
        throw new Error('Failed to fetch jewelry options');
      }
      return response.data;
    },
    retry: 1
  });

  // Remove all client-side filtering and sorting
  const filteredDiamonds = diamondsRaw?.data || [];

  if (isLoading) {
    return <LoadingState message="Loading diamonds..." />;
  }

  if (error) {
    return (
      <ErrorState
        title="Failed to load diamonds"
        description="Unable to load diamond inventory. Please check your connection and try again."
        onRetry={refetch}
        error={error as Error}
      />
    );
  }

  const totalDiamonds = diamondsRaw?.total ?? 0;
  let totalPages = Math.ceil(totalDiamonds / pageSize);
  if (!isFinite(totalPages) || totalPages < 1) totalPages = 1;

  // Refactored table row rendering logic
  const renderTableRows = (diamonds: Diamond[]) => (
    diamonds.map((diamond) => (
      <tr key={diamond.id} className="hover:bg-gray-50">
        <td className="px-6 py-4 whitespace-nowrap">
          <div>
            <div className="text-sm font-medium text-gray-900">{diamond.shape}</div>
            <div className="text-sm text-gray-500">{diamond.size_mm.includes('*') ? diamond.size_mm : `${diamond.size_mm}mm`}</div>
          </div>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <div>
            <div className="text-sm font-medium text-gray-900">{diamond.carat} ct</div>
            <div className="text-sm text-gray-500">{diamond.clarity}</div>
          </div>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <div>
            <div className="text-sm font-medium text-gray-900">{diamond.color}</div>
            <div className="text-sm text-gray-500 truncate max-w-xs" title={diamond.certificate_no}>{diamond.certificate_no}</div>
          </div>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <span className={`text-sm font-medium ${diamond.quantity <= 2 ? 'text-red-600 font-bold' : 'text-gray-900'}`}>{diamond.quantity}</span>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            diamond.status === 'in_stock' ? 'bg-green-100 text-green-800' :
            diamond.status === 'reserved' ? 'bg-yellow-100 text-yellow-800' :
            'bg-red-100 text-red-800'
          }`}>
            {diamond.status}
          </span>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <div className="text-sm font-medium text-gray-900">{diamond.vendorName || 'N/A'}</div>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <span className="text-sm text-gray-900">{diamond.purchase_date ? format(new Date(diamond.purchase_date), 'dd MMM yyyy') : '-'}</span>
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              aria-label="Edit diamond"
              onClick={() => handleEdit(diamond)}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              aria-label="Deduct stock"
              onClick={() => handleDeduct(diamond)}
              disabled={diamond.quantity === 0 || deleteMutation.isPending}
              isLoading={deleteMutation.isPending && selectedDiamond?.id === diamond.id}
            >
              <Minus className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              aria-label="Assign to manufacturing or jewelry"
              onClick={() => handleAssign(diamond)}
              title="Assign to Manufacturing or Jewelry"
            >
              <Link2 className="h-4 w-4 text-blue-600" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              aria-label="Delete diamond"
              onClick={() => handleDelete(diamond)}
              disabled={deleteMutation.isPending}
              isLoading={deleteMutation.isPending && selectedDiamond?.id === diamond.id}
            >
              <Trash2 className="h-4 w-4 text-red-600" />
            </Button>
          </div>
        </td>
      </tr>
    ))
  );

  const manufacturingOptions = manufacturingTypes?.map((type) => ({
    value: type.id.toString(),
    label: type.name
  }));

  const jewelryOptionsMapped = jewelryOptions?.map((item) => ({
    value: item.id.toString(),
    label: item.name
  }));

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Diamonds</h1>
          <p className="text-gray-600">Manage your diamond inventory</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleExportCSV} variant="secondary">
            Export CSV
          </Button>
          <Button onClick={() => setIsAddModalOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Diamond
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
          <Input
            label="Search"
            placeholder="Search diamonds..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <Select
            label="Shape"
            options={isLoadingShapes ? [] : shapeOptions}
            value={shapeFilter}
            onChange={(e) => setShapeFilter(e.target.value)}
            placeholder="Filter by shape"
          />
          <Select
            label="Status"
            options={statusOptions}
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            placeholder="Filter by status"
          />
          <Input
            label="Min Carat"
            type="number"
            value={caratMin}
            onChange={e => setCaratMin(e.target.value)}
            placeholder="Min"
          />
          <Input
            label="Max Carat"
            type="number"
            value={caratMax}
            onChange={e => setCaratMax(e.target.value)}
            placeholder="Max"
          />
          <Select
            label="Vendor"
            options={vendorOptions || []}
            value={vendorFilter}
            onChange={e => setVendorFilter(e.target.value)}
            placeholder="Filter by vendor"
          />
          <Button
            variant="ghost"
            onClick={() => {
              setSearchTerm('');
              setShapeFilter('');
              setStatusFilter('');
              setCaratMin('');
              setCaratMax('');
              setVendorFilter('');
            }}
          >
            Clear Filters
          </Button>
        </div>
      </Card>

      {/* Diamond List */}
      {filteredDiamonds.length === 0 ? (
        <EmptyState
          title="No diamonds found"
          description={
            searchTerm || shapeFilter || statusFilter
              ? "No diamonds match your search criteria. Try adjusting your filters."
              : "You haven't added any diamonds yet. Start by adding your first diamond to the inventory."
          }
          action={{
            label: "Add Diamond",
            onClick: () => setIsAddModalOpen(true)
          }}
        />
      ) : (
        <Card padding={false}>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Shape & Size
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => { setSortBy('carat'); setSortDir(sortBy === 'carat' && sortDir === 'asc' ? 'desc' : 'asc'); }}>
                    Carat & Clarity {sortBy === 'carat' && (sortDir === 'asc' ? '▲' : '▼')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Color & Certificate
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => { setSortBy('quantity'); setSortDir(sortBy === 'quantity' && sortDir === 'asc' ? 'desc' : 'asc'); }}>
                    Quantity {sortBy === 'quantity' && (sortDir === 'asc' ? '▲' : '▼')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Vendor
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => { setSortBy('purchase_date'); setSortDir(sortBy === 'purchase_date' && sortDir === 'asc' ? 'desc' : 'asc'); }}>
                    Purchase Date {sortBy === 'purchase_date' && (sortDir === 'asc' ? '▲' : '▼')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {renderTableRows(filteredDiamonds)}
              </tbody>
            </table>
          </div>
          {/* Pagination Controls */}
          <div className="flex items-center justify-between px-6 py-4 border-t bg-gray-50">
            <div className="text-sm text-gray-600">
              Showing {filteredDiamonds.length > 0 ? ((page - 1) * pageSize + 1) : 0}
              -{(page - 1) * pageSize + filteredDiamonds.length} of {totalDiamonds} diamonds
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setPage((p) => Math.max(1, p - 1))}
                disabled={page === 1}
              >
                Previous
              </Button>
              <span className="text-sm">
                Page {page} of {totalPages}
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
                disabled={page === totalPages || totalDiamonds === 0}
              >
                Next
              </Button>
              <Select
                label="Page Size"
                options={[10, 20, 50, 100].map((n) => ({ value: n.toString(), label: `${n} / page` }))}
                value={pageSize.toString()}
                onChange={(e) => {
                  setPageSize(Number(e.target.value));
                  setPage(1);
                }}
                className="w-28"
              />
            </div>
          </div>
        </Card>
      )}

      {/* Modals */}
      <Modal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        title="Add New Diamond"
        size="lg"
      >
        <DiamondForm
          onSuccess={() => {
            setIsAddModalOpen(false);
            queryClient.invalidateQueries({ queryKey: ['diamonds'] });
          }}
        />
      </Modal>

      <Modal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedDiamond(null);
        }}
        title="Edit Diamond"
        size="lg"
      >
        {selectedDiamond && (
          <DiamondForm
            diamond={selectedDiamond}
            onSuccess={() => {
              setIsEditModalOpen(false);
              setSelectedDiamond(null);
              queryClient.invalidateQueries({ queryKey: ['diamonds'] });
            }}
          />
        )}
      </Modal>

      <Modal
        isOpen={isDeductModalOpen}
        onClose={() => {
          setIsDeductModalOpen(false);
          setSelectedDiamond(null);
        }}
        title="Deduct Stock"
        size="md"
      >
        {selectedDiamond && (
          <DeductStockForm
            diamond={selectedDiamond}
            onSuccess={() => {
              setIsDeductModalOpen(false);
              setSelectedDiamond(null);
              queryClient.invalidateQueries({ queryKey: ['diamonds'] });
            }}
          />
        )}
      </Modal>

      {/* Deduct Confirmation Modal */}
      <Modal
        isOpen={isDeductConfirmOpen}
        onClose={cancelDeduct}
        title="Confirm Deduct Stock"
        size="sm"
      >
        <div className="space-y-4">
          <p>Are you sure you want to deduct stock for this diamond?</p>
          <div className="flex justify-end space-x-2">
            <Button variant="secondary" onClick={cancelDeduct}>Cancel</Button>
            <Button variant="danger" onClick={confirmDeduct}>Yes, Deduct</Button>
          </div>
        </div>
      </Modal>

      <Modal
        isOpen={isAssignModalOpen}
        onClose={() => {
          setIsAssignModalOpen(false);
          setSelectedDiamond(null);
          setSelectedManufacturing(null);
          setSelectedJewelry(null);
        }}
        title={`Assign Diamond: ${selectedDiamond?.shape} ${selectedDiamond?.carat}ct`}
        size="md"
      >
        {selectedDiamond && (
          <div className="space-y-6">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">Diamond Details</h3>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>Shape: {selectedDiamond.shape}</div>
                <div>Carat: {selectedDiamond.carat}</div>
                <div>Color: {selectedDiamond.color}</div>
                <div>Clarity: {selectedDiamond.clarity}</div>
                <div>Quantity: {selectedDiamond.quantity}</div>
                <div>Status: {selectedDiamond.status}</div>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-900 mb-3">Assign to Manufacturing</h3>
                <Select
                  label="Select Manufacturing"
                  options={manufacturingOptions || []}
                  value={selectedManufacturing || ''}
                  onChange={(e) => setSelectedManufacturing(Number(e.target.value) || null)}
                  placeholder="Choose manufacturing..."
                />
                <Button 
                  onClick={() => assignManufacturingMutation.mutate()} 
                  isLoading={assignManufacturingMutation.isPending}
                  disabled={!selectedManufacturing}
                  className="mt-2 w-full"
                >
                  Assign to Manufacturing
                </Button>
              </div>

              <div className="border-t pt-4">
                <h3 className="font-medium text-gray-900 mb-3">Assign to Jewelry</h3>
                <Select
                  label="Select Jewelry"
                  options={jewelryOptionsMapped || []}
                  value={selectedJewelry || ''}
                  onChange={(e) => setSelectedJewelry(Number(e.target.value) || null)}
                  placeholder="Choose jewelry..."
                />
                <Button 
                  onClick={() => assignJewelryMutation.mutate()} 
                  isLoading={assignJewelryMutation.isPending}
                  disabled={!selectedJewelry}
                  className="mt-2 w-full"
                >
                  Assign to Jewelry
                </Button>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default DiamondList;