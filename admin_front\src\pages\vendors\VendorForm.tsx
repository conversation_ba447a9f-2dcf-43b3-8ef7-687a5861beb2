import React from 'react';
import { useForm } from 'react-hook-form';
import { useMutation } from '@tanstack/react-query';
import { api } from '../../lib/api';
import { Vendor } from '../../types';
import Input from '../../components/ui/Input';
import Button from '../../components/ui/Button';
import toast from 'react-hot-toast';

interface VendorFormProps {
  vendor?: Vendor;
  onSuccess: () => void;
}

interface VendorFormData {
  name: string;
  gst_number: string;
  contact_number: string;
  address: string;
}

const VendorForm: React.FC<VendorFormProps> = ({ vendor, onSuccess }) => {
  const isEditing = !!vendor;

  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<VendorFormData>({
    defaultValues: vendor ? {
      name: vendor.name,
      gst_number: vendor.gst_number,
      contact_number: vendor.contact_number,
      address: vendor.address
    } : {}
  });

  const mutation = useMutation({
    mutationFn: async (data: VendorFormData) => {
      if (isEditing) {
        await api.vendors.update(vendor.id, data);
      } else {
        await api.vendors.create(data);
      }
    },
    onSuccess: () => {
      toast.success(`Vendor ${isEditing ? 'updated' : 'created'} successfully`);
      onSuccess();
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || `Failed to ${isEditing ? 'update' : 'create'} vendor`;
      toast.error(message);
    }
  });

  const onSubmit = (data: VendorFormData) => {
    mutation.mutate(data);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="md:col-span-2">
          <Input
            label="Vendor Name"
            type="text"
            required
            {...register('name', { 
              required: 'Vendor name is required',
              minLength: { value: 2, message: 'Name must be at least 2 characters' }
            })}
            error={errors.name?.message}
          />
        </div>

        <Input
          label="GST Number"
          type="text"
          required
          {...register('gst_number', { 
            required: 'GST number is required',
            pattern: {
              value: /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/,
              message: 'Invalid GST number format'
            }
          })}
          error={errors.gst_number?.message}
          helperText="Format: 22AAAAA0000A1Z5"
        />

        <Input
          label="Contact Number"
          type="tel"
          required
          {...register('contact_number', { 
            required: 'Contact number is required',
            pattern: {
              value: /^[+]?[0-9]{10,15}$/,
              message: 'Invalid contact number format'
            }
          })}
          error={errors.contact_number?.message}
        />

        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Address <span className="text-red-500">*</span>
          </label>
          <textarea
            {...register('address', { 
              required: 'Address is required',
              minLength: { value: 10, message: 'Address must be at least 10 characters' }
            })}
            rows={3}
            className={`
              w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400
              focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
              disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed
              ${errors.address ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : ''}
            `}
            placeholder="Enter complete address..."
          />
          {errors.address && (
            <p className="mt-1 text-sm text-red-600">{errors.address.message}</p>
          )}
        </div>
      </div>

      <div className="flex justify-end space-x-4">
        <Button
          type="submit"
          isLoading={mutation.isPending}
        >
          {isEditing ? 'Update Vendor' : 'Create Vendor'}
        </Button>
      </div>
    </form>
  );
};

export default VendorForm;